{"pref_camera2_scenemode_key": {"_license": ["Copyright (c) 2016, The Linux Foundation. All rights reserved.", "", "Redistribution and use in source and binary forms, with or without", "modification, are permitted provided that the following conditions are", "met:", "* Redistributions of source code must retain the above copyright", "notice, this list of conditions and the following disclaimer.", "* Redistributions in binary form must reproduce the above", "copyright notice, this list of conditions and the following", "disclaimer in the documentation and/or other materials provided", "with the distribution.", "* Neither the name of The Linux Foundation nor the names of its", "contributors may be used to endorse or promote products derived", "from this software without specific prior written permission.", "", "THIS SOFTWARE IS PROVIDED \"AS IS\" AND ANY EXPRESS OR IMPLIED", "WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF", "MERCHANTABILITY, FITNE<PERSON> FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT", "ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS", "BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR", "CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF", "SU<PERSON><PERSON>TU<PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR", "BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,", "WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE", "OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN", "IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE."], "default": {"pref_camera2_coloreffect_key": "0", "pref_camera2_flashmode_key": "off", "pref_camera2_whitebalance_key": "1", "pref_camera2_exposure_key": "0", "pref_camera2_clearsight_key": "off", "pref_camera2_mono_preview_key": "off", "pref_camera2_mpo_key": "off", "pref_camera2_longshot_key": "off"}, "0": {"pref_camera2_clearsight_key": "off", "pref_camera2_mono_preview_key": "off", "pref_camera2_mpo_key": "off"}, "3": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "4": {"pref_camera2_coloreffect_key": "0", "pref_camera2_zsl_key": "hal-zsl"}, "5": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "8": {"pref_camera2_coloreffect_key": "0"}, "9": {"pref_camera2_coloreffect_key": "0"}, "10": {"pref_camera2_coloreffect_key": "0", "pref_camera2_zsl_key": "hal-zsl"}, "13": {"pref_camera2_coloreffect_key": "0"}, "15": {"pref_camera2_coloreffect_key": "0"}, "18": {"pref_camera2_coloreffect_key": "0", "pref_camera2_zsl_key": "disable", "pref_camera2_picture_format_key": "0", "pref_camera2_multi_camera_mode_key": "0", "pref_camera2_ai_denoiser_key": "0", "pref_camera2_longshot_key": "off"}, "100": {"pref_camera2_longshot_key": "off", "pref_camera2_mono_only_key": "off", "pref_camera2_flashmode_key": "off", "pref_camera2_coloreffect_key": "0"}, "101": {"pref_camera2_coloreffect_key": "0"}, "102": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "103": {"pref_camera2_coloreffect_key": "0"}, "104": {"pref_camera2_coloreffect_key": "0"}, "105": {"pref_camera2_flashmode_key": "off", "pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "106": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "107": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "108": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}, "109": {"pref_camera2_coloreffect_key": "0", "pref_camera2_flashmode_key": "off"}, "110": {"pref_camera2_coloreffect_key": "0", "pref_camera2_longshot_key": "off"}}, "pref_camera2_clearsight_key": {"off": {}, "on": {"pref_camera2_mpo_key": "on"}}, "pref_camera2_video_time_lapse_frame_interval_key": {"default": {"pref_camera2_hfr_key": "off"}, "0": {}}, "pref_camera2_makeup_key": {"0": {}, "default": {"pref_camera2_facedetection_key": "on", "pref_camera2_picturesize_key": "3264x2448", "pref_camera2_video_quality_key": "720x480", "pref_camera2_longshot_key": "off"}}, "pref_camera2_selfiemirror_key": {"on": {"pref_camera2_longshot_key": "off"}, "off": {}}, "pref_camera2_longshot_key": {"on": {"pref_camera2_flashmode_key": "off", "pref_selfie_flash_key": "off"}, "off": {}}, "pref_camera2_zsl_key": {"app-zsl": {"pref_camera2_saveraw_key": "disable", "pref_camera2_multi_camera_mode_key": "0"}, "hal-zsl": {}, "disable": {}}, "pref_camera2_videoencoder_key": {"mpeg-4-sp": {"pref_camera2_videoencoderprofile_key": "off"}, "h263": {"pref_camera2_videoencoderprofile_key": "off"}, "h264": {"pref_camera2_videoencoderprofile_key": "off"}, "h265": {}}, "pref_camera2_capture_mfnr_key": {"1": {"pref_camera2_manual_exp_key": "off", "pref_camera2_selfiemirror_key": "off"}, "0": {"pref_camera2_ai_denoiser_key": "0"}}, "pref_camera2_ai_denoiser_key": {"1": {"pref_camera2_saveraw_key": "disable"}, "0": {"pref_camera2_ai_denoiser_mode_key": "1", "pref_camera2_ai_denoiser_format_key": "1"}}, "pref_camera2_saveraw_key": {"enable": {"pref_camera2_longshot_key": "off"}, "disable": {}}, "pref_camera2_hdr_key": {"enable": {"pref_camera2_live_preview_key": "0"}, "disable": {}}, "pref_camera2_live_preview_key": {"1": {"pref_camera2_hdr_key": "disable"}, "0": {}}, "pref_camera2_hfr_key": {"hfr90": {"pref_camera2_dis_key": "off"}, "hfr120": {"pref_camera2_dis_key": "off"}, "hfr240": {"pref_camera2_dis_key": "off"}, "hfr480": {"pref_camera2_dis_key": "off"}, "hfr960": {"pref_camera2_dis_key": "off"}, "hsr90": {"pref_camera2_dis_key": "off"}, "hsr120": {"pref_camera2_dis_key": "off"}, "hsr240": {"pref_camera2_dis_key": "off"}, "hsr480": {"pref_camera2_dis_key": "off"}, "hsr960": {"pref_camera2_dis_key": "off"}}, "pref_camera2_mfhdr_key": {"1": {"pref_camera2_zsl_key": "hal-zsl", "pref_camera2_coloreffect_key": "0", "pref_camera2_saveraw_key": "disable", "pref_camera2_manual_exp_key": "off", "pref_camera2_video_hdr_key": "0", "pref_camera2_manual_wb_key": "0", "pref_camera2_flashmode_key": "off", "pref_camera2_video_flashmode_key": "off"}, "2": {"pref_camera2_zsl_key": "hal-zsl", "pref_camera2_coloreffect_key": "0", "pref_camera2_saveraw_key": "disable", "pref_camera2_manual_exp_key": "off", "pref_camera2_video_hdr_key": "0", "pref_camera2_manual_wb_key": "0", "pref_camera2_flashmode_key": "off", "pref_camera2_video_flashmode_key": "off"}}, "pref_camera2_eis_key": {"disable": {}, "V2": {}, "V3": {"pref_camera2_video_duration_key": "0"}, "V3SetWhenPause": {"pref_camera2_video_duration_key": "0"}}, "pref_camera2_multi_camera_mode_key": {"1": {"pref_camera2_facedetection_key": "off", "pref_camera2_picture_format_key": "0", "pref_camera2_touch_track_focus_key": "off", "pref_camera2_video_time_lapse_frame_interval_key": "0", "pref_camera2_video_duration_key": "0", "pref_camera2_videoencoder_key": "h264", "pref_camera2_audioencoder_key": "aac", "pref_camera2_noise_reduction_key": "high-quality", "pref_camera2_video_rotation_key": "0", "pref_camera2_flashmode_key": "off", "pref_camera2_longshot_key": "off", "pref_camera2_hdr_key": "disable", "pref_camera2_scenemode_key": "0", "pref_camera2_timer_key": "0"}, "0": {}}, "pref_camera2_facedetection_key": {"on": {"pref_camera2_touch_track_focus_key": "off", "pref_camera2_statsnn_control_key": "0"}, "off": {}}, "pref_camera2_raw_reprocess_key": {"0": {"pref_camera2_rawinfo_type_key": "off", "pref_camera2_raw_format_key": "off"}, "1": {}, "2": {}, "3": {}, "4": {}, "5": {}}, "pref_camera2_swpdpc_key": {"1": {"pref_camera2_longshot_key": "off", "pref_camera2_zsl_key": "hal-zsl"}, "0": {}}, "pref_camera2_select_mode_key": {"rtb": {"pref_camera2_touch_track_focus_key": "off"}}}