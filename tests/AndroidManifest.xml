<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2010 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.camera.tests">

    <application
        android:debuggable="true">
        <uses-library android:name="android.test.runner" />
    </application>

    <instrumentation android:name="android.test.InstrumentationTestRunner"
             android:targetPackage="org.codeaurora.snapcam"
             android:label="Tests for Camera2 application."/>

    <instrumentation android:name="com.android.camera.CameraTestRunner"
            android:targetPackage="org.codeaurora.snapcam"
            android:label="Camera continuous test runner"/>

    <instrumentation android:name="com.android.camera.exif.ExifTestRunner"
            android:targetPackage="org.codeaurora.snapcam"
            android:label="Tests for ExifParser."/>

    <instrumentation android:name="com.android.camera.jpegstream.JpegStreamTestRunner"
            android:targetPackage="org.codeaurora.snapcam"
            android:label="Tests for JpegStream classes."/>

    <instrumentation android:name="com.android.camera.stress.CameraStressTestRunner"
            android:targetPackage="org.codeaurora.snapcam"
            android:label="Camera stress test runner"/>
</manifest>
