<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/camera_controls"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/default_background" >

    <ImageView
        android:id="@+id/shutter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/shutter_offset"
        android:src="@drawable/btn_new_shutter" />

    <include layout="@layout/menu_indicators_keyguard"
        android:layout_width="64dip"
        android:layout_height="64dip"
        android:layout_toRightOf="@id/shutter"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="9dip"
        android:layout_marginRight="-5dip" />

    <ImageView
        android:id="@+id/camera_switcher"
        style="@style/SwitcherButton"
        android:layout_toLeftOf="@id/shutter"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="3dip"
        android:scaleType="center"
        android:contentDescription="@string/accessibility_mode_picker"
        android:src="@drawable/ic_switch_camera" />

    <ImageView
        android:id="@+id/camera_switcher_ind"
        style="@style/SwitcherButton"
        android:layout_toLeftOf="@id/shutter"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="3dip"
        android:scaleType="center"
        android:contentDescription="@string/accessibility_mode_picker"
        android:src="@drawable/ic_switcher_menu_indicator" />

</RelativeLayout>
