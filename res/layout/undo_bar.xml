<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- This layout is shared by phone and tablet in portrait or landscape orientation. -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/camera_undo_deletion_bar"
        android:orientation="horizontal"
        android:visibility="gone"
        android:layout_gravity="bottom"
        style="@style/UndoBar">

    <TextView android:text="@string/deleted"
            style="@style/UndoBarTextAppearance"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="left|center_vertical" />

    <View style="@style/UndoBarSeparator" />

    <TextView android:id="@+id/camera_undo_deletion_button"
            style="@style/UndoButton"
            android:text="@string/undo"
            android:drawableLeft="@drawable/ic_menu_revert_holo_dark"/>
</LinearLayout>
