<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.RotatableLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/CameraControls"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="2dip">
    <com.android.camera.ui.RotateImageView android:id="@+id/btn_done"
        style="@style/ReviewControlIcon"
        android:contentDescription="@string/accessibility_review_ok"
        android:visibility="gone"
        android:scaleType="center"
        android:layout_gravity="right|bottom"
        android:background="@drawable/bg_pressed"
        android:src="@drawable/ic_menu_done_holo_light" />

    <com.android.camera.ui.RotateImageView android:id="@+id/btn_retake"
        style="@style/ReviewControlIcon"
        android:contentDescription="@string/accessibility_review_retake"
        android:layout_gravity="bottom|center_horizontal"
        android:scaleType="center"
        android:focusable="true"
        android:visibility="gone"
        android:background="@drawable/bg_pressed"
        android:src="@drawable/ic_btn_shutter_retake" />

    <com.android.camera.ui.RotateImageView android:id="@+id/btn_cancel"
        style="@style/ReviewControlIcon"
        android:contentDescription="@string/accessibility_review_cancel"
        android:visibility="gone"
        android:scaleType="center"
        android:layout_gravity="left|bottom"
        android:background="@drawable/bg_pressed"
        android:src="@drawable/ic_menu_cancel_holo_light" />
</com.android.camera.ui.RotatableLayout>
