<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.CameraControls xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/camera_controls"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <View
        android:id="@+id/blocker"
        android:layout_width="match_parent"
        android:layout_height="@dimen/switcher_size"
        android:layout_gravity="bottom"
        android:clickable="true" />

    <include
        android:layout_width="64dip"
        android:layout_height="64dip"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="8dip"
        android:layout_marginRight="-8dip"
        layout="@layout/menu_indicators" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/menu"
        style="@style/MenuButton"
        android:contentDescription="@string/accessibility_menu_button"
        android:src="@drawable/ic_settings" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/mute_button"
        style="@style/MenuButton"
        android:contentDescription="@string/mute_button_desc"
        android:src="@drawable/ic_unmuted_button" />

    <com.android.camera.ui.ModuleSwitcher
        android:id="@+id/camera_switcher"
        style="@style/SwitcherButton"
        android:layout_gravity="bottom|left"
        android:layout_marginBottom="2dip"
        android:scaleType="center"
        android:contentDescription="@string/accessibility_mode_picker" />

    <com.android.camera.ShutterButton
        android:id="@+id/shutter_button"
        android:layout_width="@dimen/shutter_size"
        android:layout_height="@dimen/shutter_size"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="@dimen/shutter_offset"
        android:clickable="true"
        android:contentDescription="@string/accessibility_shutter_button"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:src="@drawable/btn_new_shutter" />

    <ImageView
        android:id="@+id/video_button"
        android:visibility="invisible"
        android:layout_width="@dimen/shutter_size"
        android:layout_height="@dimen/shutter_size"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="@dimen/shutter_offset"
        android:clickable="true"
        android:contentDescription="@string/accessibility_shutter_button"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:src="@drawable/btn_new_shutter_video" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/exit_panorama"
        style="@style/MenuButton"
        android:src="@drawable/exit_panorama" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/preview_thumb"
        android:layout_width="@dimen/capture_size"
        android:layout_height="@dimen/capture_size"
        android:layout_gravity="top|right"
        android:background="@android:color/transparent"
        android:contentDescription="@string/switch_photo_filmstrip"
        android:scaleType="fitCenter" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/front_back_switcher"
        style="@style/ToggleButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/hdr_switcher"
        style="@style/ToggleButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/scene_mode_switcher"
        style="@style/ToggleButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/filter_mode_switcher"
        style="@style/ToggleButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/ts_makeup_switcher"
        style="@style/ToggleButton" />

    <LinearLayout
        android:id="@+id/remaining_photos"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:orientation="horizontal"
        android:visibility="gone" >

        <TextView
            android:id="@+id/remaining_photos_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:textColor="@android:color/white"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/remaining_photos_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/remaining_sheets"
            android:visibility="gone" />
    </LinearLayout>

</com.android.camera.ui.CameraControls>
