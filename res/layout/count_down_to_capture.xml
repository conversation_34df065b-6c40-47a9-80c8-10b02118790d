<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.CountDownView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/count_down_to_capture"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="invisible" >
    <TextView android:id="@+id/remaining_seconds"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:textSize="160sp"
        android:textColor="@android:color/white"
        android:gravity="center" />
    <TextView android:id="@+id/count_down_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/count_down_title_margin_top"
        android:gravity="center_horizontal"
        android:textSize="20sp"
        android:textColor="@android:color/white"
        android:text="@string/count_down_title_text" />
</com.android.camera.ui.CountDownView>
