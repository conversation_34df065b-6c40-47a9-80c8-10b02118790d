<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/on_screen_indicators"
    android:layout_width="64dip"
    android:layout_height="64dip" >

    <ImageView
        android:id="@+id/menu_scenemode_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="left|top"
        android:src="@drawable/ic_indicator_sce_off" />

    <ImageView
        android:id="@+id/menu_timer_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="center_horizontal|top"
        android:src="@drawable/ic_indicator_timer_off" />

    <ImageView
        android:id="@+id/menu_flash_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="right|top"
        android:src="@drawable/ic_indicator_flash_off" />

    <ImageView
        android:id="@+id/menu_exposure_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="left|bottom"
        android:src="@drawable/ic_indicator_ev_0" />

    <ImageView
        android:id="@+id/menu_location_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="center_horizontal|bottom"
        android:src="@drawable/ic_indicator_loc_on" />

    <ImageView
        android:id="@+id/menu_wb_indicator"
        style="@style/MenuIndicator"
        android:layout_gravity="right|bottom"
        android:src="@drawable/ic_indicator_wb_off" />

</FrameLayout>
