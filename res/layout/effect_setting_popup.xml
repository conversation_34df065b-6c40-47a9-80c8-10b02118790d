<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.EffectSettingPopup xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/SettingPopupWindow">
    <LinearLayout android:orientation="vertical"
            android:background="@color/popup_background"
            android:layout_height="wrap_content"
            android:layout_width="@dimen/big_setting_popup_window_width">
        <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/popup_title_frame_min_height">
            <TextView android:id="@+id/title"
                    style="@style/PopupTitleText" />
        </FrameLayout>
        <View style="@style/PopupTitleSeparator" />
        <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                <TextView android:id="@+id/clear_effects"
                        android:text="@string/clear_effects"
                        style="@style/EffectSettingTypeTitle"
                        android:textSize="@dimen/effect_setting_clear_text_size"
                        android:minHeight="@dimen/effect_setting_clear_text_min_height"
                        android:background="@drawable/bg_pressed"/>
                <TextView android:id="@+id/effect_silly_faces_title"
                        android:text="@string/effect_silly_faces"
                        android:visibility="gone"
                        style="@style/EffectSettingTypeTitle"/>
                <View android:id="@+id/effect_silly_faces_title_separator"
                        android:visibility="gone"
                        style="@style/EffectTypeSeparator"/>
                <com.android.camera.ui.ExpandedGridView android:id="@+id/effect_silly_faces"
                        style="@style/EffectSettingGrid"/>
                <View android:id="@+id/effect_background_separator"
                        android:visibility="gone"
                        style="@style/EffectTitleSeparator"/>
                <TextView android:id="@+id/effect_background_title"
                        android:text="@string/effect_background"
                        android:visibility="gone"
                        style="@style/EffectSettingTypeTitle"/>
                <View android:id="@+id/effect_background_title_separator"
                        android:visibility="gone"
                        style="@style/EffectTypeSeparator"/>
                <com.android.camera.ui.ExpandedGridView android:id="@+id/effect_background"
                        android:visibility="gone"
                        style="@style/EffectSettingGrid"/>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</com.android.camera.ui.EffectSettingPopup>
