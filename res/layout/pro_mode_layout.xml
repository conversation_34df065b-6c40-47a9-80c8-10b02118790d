<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-2017 The Linux Foundation. All rights reserved.

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are
     met:
         * Redistributions of source code must retain the above copyright
           notice, this list of conditions and the following disclaimer.
         * Redistributions in binary form must reproduce the above
           copyright notice, this list of conditions and the following
           disclaimer in the documentation and/or other materials provided
           with the distribution.
         * Neither the name of The Linux Foundation nor the names of its
           contributors may be used to endorse or promote products derived
           from this software without specific prior written permission.

     THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
     WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     MERC<PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
     ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
     BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEMPLAR<PERSON>, OR
     <PERSON>NSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
     BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
     OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
     IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pro_mode_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:visibility="invisible">

    <com.android.camera.ui.RotateLayout
        android:id="@+id/exposure_rotate_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/exposure_value"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:drawableTop="@drawable/promode_exposure"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_weight="1"/>

    </com.android.camera.ui.RotateLayout>

    <com.android.camera.ui.RotateLayout
        android:id="@+id/manual_rotate_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/manual_value"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:drawableTop="@drawable/promode_manual"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_weight="1" />

    </com.android.camera.ui.RotateLayout>

    <com.android.camera.ui.RotateLayout
        android:id="@+id/white_balance_rotate_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/white_balance_value"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="true"
            android:drawableTop="@drawable/promode_white_balance"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_weight="1"/>

    </com.android.camera.ui.RotateLayout>

    <com.android.camera.ui.RotateLayout
        android:id="@+id/iso_rotate_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <TextView
            android:id="@+id/iso_value"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:drawableTop="@drawable/promode_iso"
            android:focusable="true"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_weight="1"/>

    </com.android.camera.ui.RotateLayout>

</LinearLayout>
