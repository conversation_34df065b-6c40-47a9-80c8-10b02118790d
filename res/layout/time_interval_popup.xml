<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2011, The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.camera.ui.TimeIntervalPopup xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/SettingPopupWindow">

    <LinearLayout android:orientation="vertical"
            android:background="@color/popup_background"
            android:layout_height="wrap_content"
            android:layout_width="@dimen/big_setting_popup_window_width">

        <LinearLayout android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_width="match_parent">
            <TextView android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:ellipsize="end"
                    android:layout_weight="1"
                    android:minHeight="@dimen/popup_title_frame_min_height"
                    style="@style/PopupTitleText" />
            <Switch
                    android:id="@+id/time_lapse_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="0"
                    android:layout_marginRight="8dp"
                    android:layout_gravity="right|center_vertical" />
        </LinearLayout>

        <View style="@style/PopupTitleSeparator" />

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            <TextView
                    android:id="@+id/set_time_interval_help_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="16dip"
                    android:paddingLeft="16dip"
                    android:paddingRight="16dip"
                    android:paddingBottom="16dip"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    android:text="@string/set_time_interval_help"/>
        </LinearLayout>

        <LinearLayout android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal" >
                <include layout="@layout/time_interval_picker"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:divider="?android:attr/dividerHorizontal"
                android:showDividers="beginning"
                android:dividerPadding="0dip">
            <Button android:id="@+id/time_lapse_interval_set_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:textAppearance="?android:attr/textAppearanceMedium"
                    style="?android:attr/buttonBarButtonStyle"
                    android:text="@string/time_lapse_interval_set" />
        </LinearLayout>
    </LinearLayout>

</com.android.camera.ui.TimeIntervalPopup>
