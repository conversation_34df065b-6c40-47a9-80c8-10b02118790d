<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/rotate_dialog_root_layout"
        android:clickable="true"
        android:gravity="center"
        android:visibility="gone"
        android:background="#55000000"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <com.android.camera.ui.RotateLayout
            android:id="@+id/rotate_dialog_layout"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" >

        <LinearLayout
                android:orientation="vertical"
                android:layout_gravity="center"
                android:background="@color/popup_background"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

            <LinearLayout android:id="@+id/rotate_dialog_title_layout"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                <TextView android:id="@+id/rotate_dialog_title"
                        style="@style/TextAppearance.DialogWindowTitle"
                        android:gravity="center_vertical"
                        android:layout_marginLeft="16dip"
                        android:layout_marginRight="16dip"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="64dp"/>
                <View style="@style/PopupTitleSeparator" />
            </LinearLayout>

            <LinearLayout
                    android:orientation="horizontal"
                    android:background="@color/popup_background"
                    android:padding="9dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                <ProgressBar
                        android:id="@+id/rotate_dialog_spinner"
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                <TextView
                        style="@style/TextAppearance.Medium"
                        android:id="@+id/rotate_dialog_text"
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
            </LinearLayout>

            <ImageView android:background="@drawable/list_divider"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            <LinearLayout android:id="@+id/rotate_dialog_button_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="48dp"
                    android:orientation="horizontal">

                <Button android:id="@+id/rotate_dialog_button2"
                        style="@style/Widget.Button.Borderless"
                        android:gravity="center"
                        android:maxLines="2"
                        android:minHeight="48dp"
                        android:textSize="14sp"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content" />
                <ImageView android:background="@drawable/list_divider"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent" />
                <Button android:id="@+id/rotate_dialog_button1"
                        style="@style/Widget.Button.Borderless"
                        android:gravity="center"
                        android:maxLines="2"
                        android:minHeight="48dp"
                        android:textSize="14sp"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content" />
            </LinearLayout>
        </LinearLayout>
    </com.android.camera.ui.RotateLayout>
</FrameLayout>
