<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (C) 2014,2015 Thundersoft Corporation
    All rights Reserved

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<com.android.camera.ui.RotateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical" >

    <LinearLayout
        android:id="@+id/border"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical" >

        <ImageView
            android:id="@+id/image"
            android:layout_width="48dip"
            android:layout_height="48dip"
            android:layout_gravity="center"
            android:gravity="center" />

        <TextView
            android:id="@+id/label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:textStyle="bold" />
    </LinearLayout>

</com.android.camera.ui.RotateLayout>