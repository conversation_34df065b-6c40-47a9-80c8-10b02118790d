<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016, The Linux Foundation. All rights reserved.
     Not a Contribution.

     Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="50dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:paddingEnd="?android:attr/scrollbarSize">

    <ImageView
        android:id="@+android:id/icon"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="center"
        android:layout_marginEnd="15dp"
        android:layout_marginStart="15dp"
        android:scaleType="fitCenter" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dip"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="6dip"
        android:layout_marginTop="6dip"
        android:layout_weight="1">

        <TextView
            android:id="@+android:id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:fadingEdge="horizontal"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textSize="14sp" />

        <TextView
            android:id="@+android:id/summary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@android:id/title"
            android:layout_below="@android:id/title"
            android:maxLines="4"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="#ff2d82e2"
            android:textSize="11sp" />

    </RelativeLayout>

    <!-- Preference should place its actual preference widget here. -->
    <LinearLayout
        android:id="@+android:id/widget_frame"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="vertical" />

</LinearLayout>
