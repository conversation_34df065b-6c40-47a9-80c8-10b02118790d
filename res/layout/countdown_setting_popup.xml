<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013, The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.camera.ui.CountdownTimerPopup xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/SettingPopupWindow">

    <LinearLayout android:orientation="vertical"
            android:background="@color/popup_background"
            android:layout_height="wrap_content"
            android:layout_width="@dimen/big_setting_popup_window_width">

    <TextView
            android:id="@+id/title"
            style="@style/PopupTitleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical|center_horizontal"
            android:minHeight="@dimen/popup_title_frame_min_height" />

    <View style="@style/PopupTitleSeparator" />

    <LinearLayout
            android:id="@+id/time_duration_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical" >

            <TextView
                android:id="@+id/set_time_interval_title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:paddingTop="5dip"
                android:text="@string/set_duration"
                android:textAppearance="?android:attr/textAppearanceMedium" />
            <!-- A number picker to set timer -->

            <NumberPicker
                android:id="@+id/duration"
                android:layout_width="160dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="16dip"
                android:layout_marginRight="16dip"
                android:focusable="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" >

            <View
                android:background="#40ffffff"
                android:layout_width="match_parent"
                android:layout_height="1dip" />
            <LinearLayout
                android:id="@+id/timer_sound"
                style="@style/SettingRow" >

                <TextView android:id="@+id/beep_title"
                    style="@style/SettingItemTitle"
                    android:text="@string/pref_camera_timer_sound_title" />

                <CheckBox android:id="@+id/sound_check_box"
                    android:layout_gravity="center_vertical|right"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent" />
            </LinearLayout>

            <View
                android:background="#40ffffff"
                android:layout_width="match_parent"
                android:layout_height="1dip" />

            <Button
                android:id="@+id/timer_set_button"
                style="?android:attr/buttonBarButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/time_lapse_interval_set"
                android:textAppearance="?android:attr/textAppearanceMedium" />
        </LinearLayout>
    </LinearLayout>
</com.android.camera.ui.CountdownTimerPopup>
