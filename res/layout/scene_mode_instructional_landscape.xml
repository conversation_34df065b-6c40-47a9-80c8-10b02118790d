<?xml version="1.0" encoding="utf-8"?>
<!--Copyright (c) 2016, The Linux Foundation. All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are
    met:
    * Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
    copyright notice, this list of conditions and the following
    disclaimer in the documentation and/or other materials provided
    with the distribution.
    * Neither the name of The Linux Foundation nor the names of its
    contributors may be used to endorse or promote products derived
    from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
    WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
    ME<PERSON><PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
    ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
    BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
    BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
    WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
    OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
    IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
    -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mode_layout_rect"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:gravity="center">
    <LinearLayout
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:orientation="horizontal"
        android:gravity="center">
        <ImageView
            android:id="@+id/scene_mode_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/scene_mode_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_toRightOf="@id/scene_mode_icon"
            android:textColor="@android:color/black"
            android:textAppearance="?android:attr/textAppearanceMedium" />

    </LinearLayout>

    <TextView
        android:id="@+id/scene_mode_instructional"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="15dp"
        android:textColor="@android:color/black"
        android:layout_below="@id/title" />
    <CheckBox
        android:id="@+id/remember_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="25dp"
        android:layout_below="@id/scene_mode_instructional"
        android:text="@string/pref_camera2_not_show_again"
        android:textColor="@android:color/black"/>

    <View
        android:id="@+id/separator"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_below="@id/remember_selected"
        android:background="#c0c0c0" />

    <Button
        android:id="@+id/scene_mode_instructional_ok"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/separator"
        android:background="@android:color/transparent"
        android:layout_marginRight="30dp"
        android:layout_alignParentRight="true"
        android:text="@string/pref_camera2_scene_mode_instructional_ok"
        android:textColor="@android:color/black"/>
</RelativeLayout>

