<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.camera.ui.InLineSettingCheckBox xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/SettingRow">
    <TextView android:id="@+id/title"
            style="@style/SettingItemTitle" />

    <!-- The Switch widget always aligns to the right, so we have to wrap it in a frame layout. -->
    <FrameLayout
            android:layout_width="@dimen/setting_item_text_width"
            android:layout_height="match_parent">
        <CheckBox android:id="@+id/setting_check_box"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="match_parent" />
    </FrameLayout>
</com.android.camera.ui.InLineSettingCheckBox>
