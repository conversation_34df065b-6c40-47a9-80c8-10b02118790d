<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/panorama_capture_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <FrameLayout
        android:id="@+id/pano_preview_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center" >

        <TextureView
            android:id="@+id/pano_preview_textureview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/pano_preview_area_border"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/ic_pan_border_fast"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical" >

        <!-- The top bar with capture indication -->
        <FrameLayout
            style="@style/PanoViewHorizontalBar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" >

            <TextView
                android:id="@+id/pano_capture_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/pano_capture_indication"
                android:textAppearance="?android:textAppearanceMedium"
                android:visibility="gone" />
        </FrameLayout>

        <View
            android:id="@+id/pano_dummy_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="@integer/SRI_pano_layout_weight"
            android:visibility="invisible" />

        <!-- The bottom bar with progress bar and direction indicators -->
        <FrameLayout
            android:id="@+id/pano_progress_layout"
            style="@style/PanoViewHorizontalBar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="top"
            android:paddingTop="20dp" >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="horizontal" >

                <ImageView
                    android:id="@+id/pano_pan_left_indicator"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="5dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:src="@drawable/pano_direction_left_indicator"
                    android:visibility="invisible" />

                <com.android.camera.PanoProgressBar
                    android:id="@+id/pano_pan_progress_bar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="9"
                    android:gravity="center_vertical"
                    android:src="@drawable/ic_pan_progression"
                    android:visibility="invisible" />

                <ImageView
                    android:id="@+id/pano_pan_right_indicator"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@id/pano_pan_progress_bar"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:src="@drawable/pano_direction_right_indicator"
                    android:visibility="invisible" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

    <!-- The hint for "Too fast" text view -->
    <TextView
        android:id="@+id/pano_capture_too_fast_textview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/pano_too_fast_prompt"
        android:textAppearance="?android:textAppearanceMedium"
        android:visibility="gone" />

</FrameLayout>
