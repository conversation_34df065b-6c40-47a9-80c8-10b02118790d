<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<!-- This layout is shared by phone and tablet in portrait or landscape orientation. -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:layout_height="match_parent"
        android:layout_width="match_parent">
    <TextView android:id="@+id/recording_time"
            style="@style/OnViewfinderLabel"
            android:gravity="center"
            android:text="@string/initial_recording_seconds"
            android:drawableLeft="@drawable/ic_recording_indicator"
            android:drawablePadding="5dp"/>
    <TextView android:id="@+id/time_lapse_label"
            android:text="@string/time_lapse_title"
            style="@style/OnViewfinderLabel"/>
</LinearLayout>
