<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pano_review_layout"
    android:orientation="vertical"
    android:visibility="invisible"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <TextView
        android:id="@+id/pano_review_indicator"
        style="@style/PanoViewHorizontalBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="@string/pano_review_rendering"
        android:textAppearance="?android:textAppearanceMedium"
        android:gravity="center" />

    <ImageView
        android:id="@+id/pano_reviewarea"
        android:scaleType="fitCenter"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="@integer/SRI_pano_layout_weight" />

    <FrameLayout
        android:id="@+id/pano_review_control"
        style="@style/PanoViewHorizontalBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <include layout="@layout/pano_review_control" />
    </FrameLayout>
</LinearLayout>

