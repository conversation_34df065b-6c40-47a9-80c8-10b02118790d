<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical" >

    <com.android.camera.tinyplanet.TinyPlanetPreview
        android:id="@+id/preview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center_horizontal"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical" >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/tiny_planet_zoom"
            android:textColor="#FFF" />

        <SeekBar
            android:id="@+id/zoomSlider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/tiny_planet_zoom"
            android:max="1000"
            android:progress="500" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="@string/tiny_planet_rotate"
            android:textColor="#FFF" />

        <SeekBar
            android:id="@+id/angleSlider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/tiny_planet_rotate"
            android:max="360"
            android:progress="0" />

        <Button
            android:id="@+id/creatTinyPlanetButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="@string/create_tiny_planet" />
    </LinearLayout>

</LinearLayout>