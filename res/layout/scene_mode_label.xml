<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016, The Linux Foundation. All rights reserved.
     Not a Contribution.

     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="90dp"
    android:layout_marginRight="20dp"
    android:gravity="right">
    <com.android.camera.ui.RotateLayout
        android:id="@+id/scene_mode_label_rect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <LinearLayout
            android:id="@+id/scene_mode_label_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp"
            android:background="#90000000"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/scene_mode_label"
                android:singleLine="true"
                android:textColor="@android:color/white"
                android:textSize="16dp"
                style="@style/OnViewfinderSceneLabel" />
            <ImageView
                android:id="@+id/scene_mode_label_close"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/x" />
        </LinearLayout>
    </com.android.camera.ui.RotateLayout>
</LinearLayout>
