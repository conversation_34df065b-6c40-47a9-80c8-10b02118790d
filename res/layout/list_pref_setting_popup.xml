<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2011, The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.ListPrefSettingPopup xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/SettingPopupWindow">

    <LinearLayout android:orientation="vertical"
            android:background="@color/popup_background"
            android:layout_height="wrap_content"
            android:layout_width="@dimen/setting_popup_window_width">

        <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/popup_title_frame_min_height">
            <TextView android:id="@+id/title"
                    style="@style/PopupTitleText" />
        </FrameLayout>

        <View style="@style/PopupTitleSeparator" />

        <FrameLayout android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <ListView android:id="@+id/settingList"
                    style="@style/SettingItemList"
                    android:choiceMode="singleChoice" />
        </FrameLayout>
    </LinearLayout>
</com.android.camera.ui.ListPrefSettingPopup>
