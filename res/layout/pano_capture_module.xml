<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016, The Linux Foundation. All rights reserved.
     Not a Contribution.

     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical|center_horizontal">
        <com.android.camera.ui.AutoFitSurfaceView
            android:id="@+id/mdp_preview_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"/>
    </FrameLayout>
    <com.android.camera.ui.PanoCaptureProcessView
        android:id="@+id/preview_process_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <include
        layout="@layout/scene_mode_label"/>

    <include
        style="@style/CameraControls"
        layout="@layout/camera_controls"
        android:layout_gravity="center" />
</merge>
