<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<com.android.camera.ui.FilmstripBottomControls
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/filmstrip_bottom_controls"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:padding="10dp"
    android:visibility="visible" >

    <ImageButton
        android:id="@+id/filmstrip_bottom_control_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:background="@drawable/photopage_bottom_button_background"
        android:paddingBottom="5dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="5dp"
        android:src="@drawable/ic_menu_edit_holo_dark"
        android:visibility="gone" />

    <ImageButton
        android:id="@+id/filmstrip_bottom_control_panorama"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/transparent_button_background"
        android:clickable="true"
        android:src="@drawable/ic_view_photosphere"
        android:visibility="gone" />

    <ImageButton
        android:id="@+id/filmstrip_bottom_control_tiny_planet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"
        android:background="@drawable/photopage_bottom_button_background"
        android:paddingBottom="5dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingTop="5dp"
        android:src="@drawable/ic_menu_tiny_planet"
        android:visibility="gone" />

</com.android.camera.ui.FilmstripBottomControls>
