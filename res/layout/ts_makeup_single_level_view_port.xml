<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (C) 2014,2015 Thundersoft Corporation
    All rights Reserved

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/id_makeup_single_level_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_gravity="bottom"
    android:orientation="vertical" >

    <SeekBar
        android:id="@+id/seekbar_makeup_level"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dip"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/tsmakeup_mode_level_size"
        android:layout_gravity="center"
        android:gravity="center" >

        <LinearLayout
            android:id="@+id/id_layout_makeup_back"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="2.0"
            android:gravity="center" >

            <ImageView
                android:id="@+id/id_iv_makeup_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_ts_makeup_back" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1.0"
            android:gravity="center" >

            <View
                android:layout_width="1dip"
                android:layout_height="36dip"
                android:layout_gravity="center"
                android:background="@drawable/ic_ts_makeup_vline" />
        </LinearLayout>

        <com.android.camera.ui.RotateLayout
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="3.5"
            android:gravity="center"
            android:orientation="vertical" >

            <LinearLayout
                android:id="@+id/id_layout_makeup_clean"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="2dip" >

                <ImageView
                    android:id="@+id/id_iv_makeup_clean"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_ts_makeup_clean_selector" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    android:singleLine="true"
                    android:text="@string/pref_camera_tsmakeup_level_clean"
                    android:textColor="@android:color/white"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </com.android.camera.ui.RotateLayout>

        <com.android.camera.ui.RotateLayout
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="3.5"
            android:gravity="center"
            android:orientation="vertical" >

            <LinearLayout
                android:id="@+id/id_layout_makeup_whiten"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="2dp" >

                <ImageView
                    android:id="@+id/id_iv_makeup_whiten"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_ts_makeup_whiten_selector" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    android:singleLine="true"
                    android:text="@string/pref_camera_tsmakeup_level_whiten"
                    android:textColor="@android:color/white"
                    android:textSize="13sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </com.android.camera.ui.RotateLayout>
    </LinearLayout>

</LinearLayout>