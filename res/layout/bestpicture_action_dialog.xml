<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (c) 2017, The Linux Foundation. All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are
  met:
      * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
      * Redistributions in binary form must reproduce the above
        copyright notice, this list of conditions and the following
        disclaimer in the documentation and/or other materials provided
        with the distribution.
      * Neither the name of The Linux Foundation nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
  WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  ME<PERSON><PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
  ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
  BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
  BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
  OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
  IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<com.android.camera.ui.BestPictureActionDialogLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dialog_background"
    android:id="@+id/mlayout">

    <TextView
        android:id="@+id/mtitle"
        android:layout_width="220dp"
        android:layout_height="30dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/dialog_text"/>


    <TextView
        android:id="@+id/content"
        android:layout_width="220dp"
        android:layout_height="50dp"
        android:layout_below="@+id/mtitle"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:textSize="15sp"
        android:textColor="@color/dialog_text"/>

    <RelativeLayout
        android:id="@+id/showagainlayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/content"
        android:layout_marginTop="10dp">

        <CheckBox
            android:id="@+id/mcheck"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="30dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="5dp"
            android:layout_toRightOf="@+id/mcheck"
            android:text="@string/dialog_do_not_show"
            android:textColor="@color/dialog_text"/>
    </RelativeLayout>


    <TextView
        android:id="@+id/hr"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/showagainlayout"
        android:layout_marginTop="10dp"
        android:background="@color/dialog_hr"/>

    <Button
        android:id="@+id/nativebt"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/hr"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="10dp"
        android:background="@null"
        android:textAllCaps="false"
        android:textColor="@color/dialog_button"/>

    <Button
        android:id="@+id/positivebt"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/hr"
        android:layout_marginBottom="10dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="10dp"
        android:background="@null"
        android:textAllCaps="false"
        android:textColor="@color/dialog_button"/>

    <Button
        android:id="@+id/okbt"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_below="@+id/hr"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="10dp"
        android:background="@null"
        android:textAllCaps="false"
        android:textColor="#8EE5EE"/>


</com.android.camera.ui.BestPictureActionDialogLayout>