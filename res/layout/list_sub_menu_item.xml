<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2014, The Linux Foundation. All rights reserved.

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are
     met:
         * Redistributions of source code must retain the above copyright
           notice, this list of conditions and the following disclaimer.
         * Redistributions in binary form must reproduce the above
           copyright notice, this list of conditions and the following
           disclaimer in the documentation and/or other materials provided
           with the distribution.
         * Neither the name of The Linux Foundation nor the names of its
           contributors may be used to endorse or promote products derived
           from this software without specific prior written permission.

     THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
     WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     ME<PERSON><PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
     ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
     BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
     CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
     BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
     OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
     IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<com.android.camera.ui.CheckedLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/CustomSettingRow"
    tools:ignore="UseCompoundDrawables" >

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/setting_item_icon_width"
        android:layout_height="@dimen/setting_item_icon_width"
        android:layout_marginRight="@dimen/setting_item_list_margin"
        android:layout_alignParentLeft="true"
        android:layout_centerInParent="true"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter" />

    <TextView
        android:id="@+id/text"
        style="@style/CustomSettingItemTitle"
        android:layout_toRightOf="@+id/image"
        android:shadowColor="@android:color/transparent"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:textColor="@android:color/white"
        android:textStyle="bold" />

</com.android.camera.ui.CheckedLinearLayout>
