<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-2017 The Linux Foundation. All rights reserved.
     Not a Contribution.

     Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<com.android.camera.ui.OneUICameraControls xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/camera_controls"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <com.android.camera.ui.ProMode
        android:id="@+id/promode_slider"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content" />

    <com.android.camera.ui.FlashToggleButton
        android:id="@+id/flash_button"
        style="@style/OneUIMenuButton"
        android:src="@drawable/flash" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/mute_button"
        style="@style/OneUIMenuButton"
        android:contentDescription="@string/mute_button_desc"
        android:src="@drawable/ic_unmuted_button"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_gravity="center_horizontal|center_vertical"
        android:visibility="gone"
        android:indeterminate="true"
        android:indeterminateTint="#f44242" />

    <com.android.camera.ShutterButton
        android:id="@+id/shutter_button"
        android:layout_width="@dimen/one_ui_bottom_large"
        android:layout_height="@dimen/one_ui_bottom_large"
        android:clickable="true"
        android:contentDescription="@string/accessibility_shutter_button"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:src="@drawable/photo_capture" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/video_button"
        android:layout_width="@dimen/one_ui_bottom_large"
        android:layout_height="@dimen/one_ui_bottom_large"
        android:clickable="true"
        android:contentDescription="@string/accessibility_shutter_button"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:visibility="gone"
        android:src="@drawable/video_capture" />

    <com.android.camera.PauseButton
        android:id="@+id/video_pause"
        android:layout_width="@dimen/one_ui_bottom_small"
        android:layout_height="@dimen/one_ui_bottom_small"
        android:focusable="true"
        android:clickable="true"
        android:scaleType="fitCenter"
        android:visibility="gone"
        android:src="@drawable/btn_pause_recording"/>

    <com.android.camera.ui.RotateImageView
        android:id="@+id/cancel_button"
        android:layout_width="@dimen/one_ui_bottom_small"
        android:layout_height="@dimen/one_ui_bottom_small"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="fitCenter"
        android:visibility="gone"
        android:src="@drawable/ic_menu_cancel_holo_light" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/front_back_switcher"
        android:layout_width="@dimen/one_ui_bottom_small"
        android:layout_height="@dimen/one_ui_bottom_small"
        android:focusable="true"
        android:clickable="true"
        android:scaleType="fitCenter"
        android:visibility="gone"
        android:src="@drawable/front_back_camera" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/preview_thumb"
        android:layout_width="@dimen/capture_size"
        android:layout_height="@dimen/capture_size"
        android:layout_gravity="top|right"
        android:background="@android:color/transparent"
        android:contentDescription="@string/switch_photo_filmstrip"
        android:scaleType="fitCenter" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/exit_best_mode"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:clickable="true"
        android:contentDescription="@string/accessibility_exit_best_mode_button"
        android:focusable="true"
        android:layout_gravity="top|left"
        android:scaleType="fitCenter"
        android:src="@drawable/x"
        android:visibility="gone"/>

    <com.android.camera.ui.RotateImageView
        android:id="@+id/hdr_switcher"
        style="@style/OneUIMenuButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/scene_mode_switcher"
        style="@style/OneUIMenuButton"
        android:src="@drawable/more_options" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/filter_mode_switcher"
        style="@style/OneUIMenuButton"
        android:src="@drawable/filters" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/ts_makeup_switcher"
        style="@style/OneUIMenuButton" />

    <com.android.camera.ui.RotateImageView
        android:id="@+id/settings"
        style="@style/OneUIMenuButton" />

    <LinearLayout
        android:id="@+id/remaining_photos"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            android:id="@+id/remaining_photos_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:textColor="@android:color/white"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/remaining_photos_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/remaining_sheets"
            android:visibility="gone" />
    </LinearLayout>

    <include layout="@layout/pro_mode_layout" />

    <SeekBar
        android:layout_width="320dp"
        android:layout_height="40dp"
        android:maxHeight="3dip"
        android:minHeight="1dip"
        android:visibility="gone"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="90dp"
        android:progressDrawable="@drawable/beautify_progressbar_style"
        android:thumb="@drawable/ic_beautify_oval"
        android:id="@+id/deepportrait_seekbar"/>

    <LinearLayout
        android:id="@+id/zoom_linearlayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:layout_gravity="center_horizontal|bottom"
        android:layout_marginBottom="140dp"
        android:orientation="vertical"
        android:visibility="gone">
        <TextView
            android:id="@+id/zoom_value_text"
            android:layout_width="50dip"
            android:layout_height="30dip"
            android:textSize="14dip"
            android:layout_gravity="center_horizontal"
            android:layout_above="@+id/zoom_seekbar"
            android:textColor="@android:color/white">
        </TextView>

        <SeekBar
            android:layout_width="320dp"
            android:layout_height="30dp"
            android:maxHeight="3dip"
            android:minHeight="1dip"
            android:visibility="gone"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:layout_gravity="center_horizontal"
            android:progressDrawable="@drawable/beautify_progressbar_style"
            android:thumb="@drawable/ic_beautify_oval"
            android:id="@id/zoom_seekbar"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/makeup_seekbar_layout"
        android:visibility="gone">
        <ImageView
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="10dp"
            android:src="@drawable/seekbar_hide"
            android:id="@+id/seekbar_toggle">
        </ImageView>
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/seekbar_body">
            <com.android.camera.ui.RotateLayout
                android:layout_width="30dp"
                android:layout_height="40dp"
                android:layout_alignParentLeft="true"
                android:id="@+id/makeup_low_text">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:text="@string/makeup_seekbar_low"
                    android:textColor="@android:color/white">
                </TextView>
            </com.android.camera.ui.RotateLayout>
            <SeekBar
                android:layout_width="220dp"
                android:layout_height="40dp"
                android:maxHeight="3dip"
                android:minHeight="1dip"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/makeup_low_text"
                android:progressDrawable="@drawable/beautify_progressbar_style"
                android:thumb="@drawable/ic_beautify_oval"
                android:id="@+id/makeup_seekbar"/>
            <com.android.camera.ui.RotateLayout
                android:layout_width="30dp"
                android:layout_height="40dp"
                android:layout_toRightOf="@+id/makeup_seekbar"
                android:layout_marginLeft="10dp"
                android:id="@+id/makeup_high_text">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:text="@string/makeup_seekbar_high"
                    android:textColor="@android:color/white">
                </TextView>
            </com.android.camera.ui.RotateLayout>
        </RelativeLayout>
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="100dp"
        android:background="@color/camera_control_bg_transparent">

        <android.support.v7.widget.RecyclerView
            android:id="@+id/mode_select_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_centerHorizontal="true"
            android:scrollbars="none" />

    </FrameLayout>

</com.android.camera.ui.OneUICameraControls>
