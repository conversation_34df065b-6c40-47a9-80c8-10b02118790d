<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2012, The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <item name="ic_effects_holo_light" type="drawable">@drawable/ic_effects_holo_light_xlarge</item>
    <item name="ic_pan_border_fast" type="drawable">@drawable/ic_pan_border_fast_xlarge</item>
    <item name="ic_pan_left_indicator_fast" type="drawable">@drawable/ic_pan_left_indicator_fast_xlarge</item>
    <item name="ic_pan_left_indicator" type="drawable">@drawable/ic_pan_left_indicator_xlarge</item>
    <item name="ic_pan_progression" type="drawable">@drawable/ic_pan_progression_xlarge</item>
    <item name="ic_pan_right_indicator_fast" type="drawable">@drawable/ic_pan_right_indicator_fast_xlarge</item>
    <item name="ic_pan_right_indicator" type="drawable">@drawable/ic_pan_right_indicator_xlarge</item>
    <item name="ic_scn_holo_light" type="drawable">@drawable/ic_scn_holo_light_xlarge</item>
    <item name="ic_snapshot_border" type="drawable">@drawable/ic_snapshot_border_xlarge</item>
    <item name="ic_switch_photo_facing_holo_light" type="drawable">@drawable/ic_switch_photo_facing_holo_light_xlarge</item>
    <item name="ic_switch_video_facing_holo_light" type="drawable">@drawable/ic_switch_video_facing_holo_light_xlarge</item>
    <item name="ic_timelapse_none" type="drawable">@drawable/ic_timelapse_none_xlarge</item>
</resources>
