<!--
    Copyright (c) 2012-2013, The Linux Foundation. All rights reserved.

    Not a Contribution.

    Copyright (C) 2008 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<resources>
    <!-- Camera Preferences Power Mode dialog box entries -->
    <string-array name="pref_camera_powermode_entries" translatable="false">
        <item>@string/pref_camera_powermode_entry_lp</item>
        <item>@string/pref_camera_powermode_entry_np</item>
    </string-array>

    <string-array name="pref_camera_powermode_entryvalues" translatable="false">
        <item>@string/pref_camera_powermode_value_lp</item>
        <item>@string/pref_camera_powermode_value_np</item>
    </string-array>

    <!-- Camera Preferences Picture format dialog box entries -->
    <string-array name="pref_camera_picture_format_entries">
        <item>@string/pref_camera_picture_format_entry_jpeg</item>
        <item>@string/pref_camera_picture_format_entry_raw</item>
        <item>@string/pref_camera_picture_format_entry_raw_yuv_422_sp</item>
        <item>@string/pref_camera_picture_format_entry_raw_yuv_8bit_yuyv</item>
        <item>@string/pref_camera_picture_format_entry_raw_yuv_8bit_yvyu</item>
        <item>@string/pref_camera_picture_format_entry_raw_yuv_8bit_uyvy</item>
        <item>@string/pref_camera_picture_format_entry_raw_yuv_8bit_vyuy</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_10gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_10grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_10rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_10bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_12gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_12grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_12rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_qcom_12bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_10gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_10grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_10rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_10bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_12gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_12grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_12rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_mipi_12bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10bggr</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12gbrg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12grbg</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12rggb</item>
        <item>@string/pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12bggr</item>
    </string-array>
    <string-array name="pref_camera_picture_format_entryvalues">
        <item>@string/pref_camera_picture_format_value_jpeg</item>
        <item>@string/pref_camera_picture_format_value_raw</item>
        <item>@string/pref_camera_picture_format_value_raw_yuv_422_sp</item>
        <item>@string/pref_camera_picture_format_value_raw_yuv_8bit_yuyv</item>
        <item>@string/pref_camera_picture_format_value_raw_yuv_8bit_yvyu</item>
        <item>@string/pref_camera_picture_format_value_raw_yuv_8bit_uyvy</item>
        <item>@string/pref_camera_picture_format_value_raw_yuv_8bit_vyuy</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_10gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_10grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_10rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_10bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_12gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_12grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_12rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_qcom_12bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_10gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_10grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_10rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_10bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_12gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_12grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_12rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_mipi_12bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_10gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_10grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_10rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_10bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_12gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_12grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_12rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_qcom_12bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_10gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_10grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_10rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_10bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_12gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_12grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_12rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_mipi_12bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain8_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain8_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain8_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain8_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_8gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_8grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_8rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_8bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_10gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_10grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_10rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_10bggr</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_12gbrg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_12grbg</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_12rggb</item>
        <item>@string/pref_camera_picture_format_value_raw_bayer_ideal_plain16_12bggr</item>
    </string-array>

    <!-- Camera Preferences JPEG quality dialog box entries -->
    <string-array name="pref_camera_jpegquality_entries" translatable="false">
        <item>@string/pref_camera_jpegquality_entry_0</item>
        <item>@string/pref_camera_jpegquality_entry_1</item>
        <item>@string/pref_camera_jpegquality_entry_2</item>
    </string-array>

    <string-array name="pref_camera_jpegquality_entryvalues" translatable="false">
        <item>55</item>
        <item>85</item>
        <item>100</item>
    </string-array>

    <!-- Rough estimates of jpeg compression ratio corresponding to qualities defined above. -->
    <integer-array name="jpegquality_compression_ratio">
        <item>48</item>
        <item>20</item>
        <item>6</item>
    </integer-array>

    <!-- Camera Preferences Color effect dialog box entries -->
    <string-array name="pref_camera_coloreffect_entries" translatable="false">
        <item>@string/pref_camera_coloreffect_entry_none</item>
        <item>@string/pref_camera_coloreffect_entry_mono</item>
        <item>@string/pref_camera_coloreffect_entry_sepia</item>
        <item>@string/pref_camera_coloreffect_entry_negative</item>
        <item>@string/pref_camera_coloreffect_entry_solarize</item>
        <item>@string/pref_camera_coloreffect_entry_posterize</item>
        <item>@string/pref_camera_coloreffect_entry_aqua</item>
        <item>@string/pref_camera_coloreffect_entry_emboss</item>
        <item>@string/pref_camera_coloreffect_entry_sketch</item>
        <item>@string/pref_camera_coloreffect_entry_neon</item>
        <item>@string/pref_camera_coloreffect_entry_pastel</item>
        <item>@string/pref_camera_coloreffect_entry_mosaic</item>
        <item>@string/pref_camera_coloreffect_entry_redtint</item>
        <item>@string/pref_camera_coloreffect_entry_bluetint</item>
        <item>@string/pref_camera_coloreffect_entry_greentint</item>
    </string-array>

    <array name="coloreffect_thumbnails" translatable="false">
        <item>@drawable/thumb_filter_nofilter</item>
        <item>@drawable/thumb_filter_monochrome</item>
        <item>@drawable/thumb_filter_sepia</item>
        <item>@drawable/thumb_filter_negative</item>
        <item>@drawable/thumb_filter_solarize</item>
        <item>@drawable/thumb_filter_posterize</item>
        <item>@drawable/thumb_filter_aqua</item>
        <item>@drawable/thumb_filter_emboss</item>
        <item>@drawable/thumb_filter_sketch</item>
        <item>@drawable/thumb_filter_neon</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
    </array>

    <string-array name="pref_camera_coloreffect_entryvalues" translatable="false">
        <item>none</item>
        <item>mono</item>
        <item>sepia</item>
        <item>negative</item>
        <item>solarize</item>
        <item>posterize</item>
        <item>aqua</item>
        <item>emboss</item>
        <item>sketch</item>
        <item>neon</item>
        <item>pastel</item>
        <item>mosaic</item>
        <item>red-tint</item>
        <item>blue-tint</item>
        <item>green-tint</item>
    </string-array>

    <!-- Camera Preferences AE Bracketing dialog box entries -->
    <string-array name="pref_camera_ae_bracket_hdr_entries">
        <item>@string/pref_camera_ae_bracket_hdr_entry_off</item>
        <item>@string/pref_camera_ae_bracket_hdr_entry_on</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_ae_bracket_hdr_entryvalues">
        <item>Off</item>
        <item>AE-Bracket</item>
    </string-array>

    <!-- Camera Preferences Touch AF/AEC dialog box entries -->
    <string-array name="pref_camera_touchafaec_entries">
        <item>@string/pref_camera_touchafaec_entry_off</item>
        <item>@string/pref_camera_touchafaec_entry_on</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_touchafaec_entryvalues">
        <item>touch-off</item>
        <item>touch-on</item>
    </string-array>

    <!-- DIS dialog box entries -->
    <string-array name="pref_camera_dis_entries">
        <item>@string/pref_camera_dis_entry_off</item>
        <item>@string/pref_camera_dis_entry_on</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_dis_entryvalues">
        <item>@string/pref_camera_dis_value_disable</item>
        <item>@string/pref_camera_dis_value_enable</item>
    </string-array>

    <!-- Face Recognition dialog box entries -->
    <string-array name="pref_camera_facerc_entries">
        <item>@string/pref_camera_facerc_entry_off</item>
        <item>@string/pref_camera_facerc_entry_on</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_facerc_entryvalues">
        <item>off</item>
        <item>on</item>
   </string-array>

    <!-- Camera Preferences MultiLevel dialog box entries -->
    <string-array name="pref_camera_sharpness_entries">
        <item>@string/pref_camera_sharpness_entry_level0</item>
        <item>@string/pref_camera_sharpness_entry_level1</item>
        <item>@string/pref_camera_sharpness_entry_level2</item>
        <item>@string/pref_camera_sharpness_entry_level3</item>
        <item>@string/pref_camera_sharpness_entry_level4</item>
        <item>@string/pref_camera_sharpness_entry_level5</item>
        <item>@string/pref_camera_sharpness_entry_level6</item>
    </string-array>

    <!-- Camera Preferences MultiLevel dialog box entries -->
    <string-array name="pref_camera_contrast_entries">
        <item>@string/pref_camera_contrast_entry_level0</item>
        <item>@string/pref_camera_contrast_entry_level1</item>
        <item>@string/pref_camera_contrast_entry_level2</item>
        <item>@string/pref_camera_contrast_entry_level3</item>
        <item>@string/pref_camera_contrast_entry_level4</item>
        <item>@string/pref_camera_contrast_entry_level5</item>
        <item>@string/pref_camera_contrast_entry_level6</item>
        <item>@string/pref_camera_contrast_entry_level7</item>
        <item>@string/pref_camera_contrast_entry_level8</item>
        <item>@string/pref_camera_contrast_entry_level9</item>
        <item>@string/pref_camera_contrast_entry_level10</item>
    </string-array>

    <!-- Camera Preferences Saturation dialog box entries -->
    <string-array name="pref_camera_saturation_entries">
        <item>@string/pref_camera_saturation_entry_level0</item>
        <item>@string/pref_camera_saturation_entry_level1</item>
        <item>@string/pref_camera_saturation_entry_level2</item>
        <item>@string/pref_camera_saturation_entry_level3</item>
        <item>@string/pref_camera_saturation_entry_level4</item>
        <item>@string/pref_camera_saturation_entry_level5</item>
        <item>@string/pref_camera_saturation_entry_level6</item>
        <item>@string/pref_camera_saturation_entry_level7</item>
        <item>@string/pref_camera_saturation_entry_level8</item>
        <item>@string/pref_camera_saturation_entry_level9</item>
        <item>@string/pref_camera_saturation_entry_level10</item>
    </string-array>

    <!-- Entry Valur array for sharpness -->
    <string-array name="pref_camera_multilevel_sharpness_entryvalues">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
    </string-array>

    <!-- Entry values array is shared between Saturation & Contrast -->
    <string-array name="pref_camera_multilevel_entryvalues">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
    </string-array>

    <!-- Camera Preferences ISO dialog box entries -->
     <string-array name="pref_camera_iso_entries">
         <item>@string/pref_camera_iso_entry_auto</item>
         <item>@string/pref_camera_iso_entry_isodeblur</item>
         <item>@string/pref_camera_iso_entry_iso100</item>
         <item>@string/pref_camera_iso_entry_iso200</item>
         <item>@string/pref_camera_iso_entry_iso400</item>
         <item>@string/pref_camera_iso_entry_iso800</item>
         <item>@string/pref_camera_iso_entry_iso1600</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_iso_entryvalues">
         <item>@string/pref_camera_iso_value_auto</item>
         <item>@string/pref_camera_iso_value_isodeblur</item>
         <item>@string/pref_camera_iso_value_iso100</item>
         <item>@string/pref_camera_iso_value_iso200</item>
         <item>@string/pref_camera_iso_value_iso400</item>
         <item>@string/pref_camera_iso_value_iso800</item>
         <item>@string/pref_camera_iso_value_iso1600</item>
     </string-array>

    <!-- Camera Preferences Anti Banding dialog box entries -->
     <string-array name="pref_camera_antibanding_entries">
         <item>@string/pref_camera_antibanding_entry_0</item>
         <item>@string/pref_camera_antibanding_entry_1</item>
         <item>@string/pref_camera_antibanding_entry_2</item>
         <item>@string/pref_camera_antibanding_entry_3</item>
     </string-array>

     <string-array name="pref_camera_antibanding_entryvalues">
         <item>off</item>
         <item>50hz</item>
         <item>60hz</item>
         <item>auto</item>
     </string-array>

     <!-- Camera Preferences Histogram dialog box entries -->
     <string-array name="pref_camera_histogram_entries">
          <item>@string/pref_camera_histogram_entry_disable</item>
          <item>@string/pref_camera_histogram_entry_enable</item>
     </string-array>

     <!-- Video Preferences High Frame Rate dialog box entries -->
    <string-array name="pref_camera_hfr_entries">
        <item>@string/pref_camera_hfr_entry_32x</item>
        <item>@string/pref_camera_hfr_entry_16x</item>
        <item>@string/pref_camera_hfr_entry_8x</item>
        <item>@string/pref_camera_hfr_entry_4x</item>
        <item>@string/pref_camera_hfr_entry_3x</item>
        <item>@string/pref_camera_hfr_entry_2x</item>
        <item>@string/pref_camera_hfr_entry_hsr_960</item>
        <item>@string/pref_camera_hfr_entry_hsr_480</item>
        <item>@string/pref_camera_hfr_entry_hsr_240</item>
        <item>@string/pref_camera_hfr_entry_hsr_120</item>
        <item>@string/pref_camera_hfr_entry_hsr_90</item>
        <item>@string/pref_camera_hfr_entry_hsr_60</item>
        <item>@string/pref_camera_hfr_entry_4x_ssm_480</item>
        <item>@string/pref_camera_hfr_entry_4x_ssm_240</item>
        <item>@string/pref_camera_hfr_entry_4x_ssm_120</item>
        <item>@string/pref_camera_hfr_entry_2x_ssm_480</item>
        <item>@string/pref_camera_hfr_entry_2x_ssm_240</item>
        <item>@string/pref_camera_hfr_entry_2x_ssm_120</item>
        <item>@string/pref_camera_hfr_entry_off</item>
    </string-array>

     <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_hfr_entryvalues">
        <item>@string/pref_camera_hfr_value_32x</item>
        <item>@string/pref_camera_hfr_value_16x</item>
        <item>@string/pref_camera_hfr_value_8x</item>
        <item>@string/pref_camera_hfr_value_4x</item>
        <item>@string/pref_camera_hfr_value_3x</item>
        <item>@string/pref_camera_hfr_value_2x</item>
        <item>@string/pref_camera_hfr_value_hsr_960</item>
        <item>@string/pref_camera_hfr_value_hsr_480</item>
        <item>@string/pref_camera_hfr_value_hsr_240</item>
        <item>@string/pref_camera_hfr_value_hsr_120</item>
        <item>@string/pref_camera_hfr_value_hsr_90</item>
        <item>@string/pref_camera_hfr_value_hsr_60</item>
        <item>@string/pref_camera_hfr_value_4x_ssm_480</item>
        <item>@string/pref_camera_hfr_value_4x_ssm_240</item>
        <item>@string/pref_camera_hfr_value_4x_ssm_120</item>
        <item>@string/pref_camera_hfr_value_2x_ssm_480</item>
        <item>@string/pref_camera_hfr_value_2x_ssm_240</item>
        <item>@string/pref_camera_hfr_value_2x_ssm_120</item>
        <item>@string/pref_camera_hfr_value_off</item>
    </string-array>

     <!-- Video Preferences Noise Reduction dialog box entries -->
     <string-array name="pref_camera_noise_reduction_entries">
          <item>@string/pref_camera_noise_reduction_entry_off</item>
          <item>@string/pref_camera_noise_reduction_entry_fast</item>
          <item>@string/pref_camera_noise_reduction_entry_high_quality</item>
     </string-array>
     <!-- Do not localize entryvalues -->
     <string-array name="pref_camera_noise_reduction_entryvalues">
          <item>@string/pref_camera_noise_reduction_value_off</item>
          <item>@string/pref_camera_noise_reduction_value_fast</item>
          <item>@string/pref_camera_noise_reduction_value_high_quality</item>
     </string-array>

     <!-- Video Preferences See More dialog box entries -->
     <string-array name="pref_camera_see_more_entries">
          <item>@string/pref_camera_see_more_entry_off</item>
          <item>@string/pref_camera_see_more_entry_on</item>
     </string-array>
     <!-- Do not localize entryvalues -->
     <string-array name="pref_camera_see_more_entryvalues">
          <item>@string/pref_camera_see_more_value_off</item>
          <item>@string/pref_camera_see_more_value_on</item>
     </string-array>

     <!-- Do not localize entryvalues -->
     <string-array name="pref_camera_histogram_entryvalues">
          <item>@string/pref_camera_histogram_value_disable</item>
          <item>@string/pref_camera_histogram_value_enable</item>
     </string-array>

    <!-- Camera Preferences selfie flash entries -->
    <string-array name="pref_selfie_flash_entries">
         <item>@string/pref_selfie_flash_entry_off</item>
         <item>@string/pref_selfie_flash_entry_on</item>
    </string-array>

    <string-array name="pref_selfie_flash_entryvalues" translatable="false">
        <item>off</item>
        <item>on</item>
    </string-array>

    <!-- Camera Preferences Face Detection dialog box entries -->
    <string-array name="pref_camera_facedetection_entries">
         <item>@string/pref_camera_facedetection_entry_off</item>
         <item>@string/pref_camera_facedetection_entry_on</item>
    </string-array>

    <string-array name="pref_camera_facedetection_entryvalues" translatable="false">
        <item>off</item>
        <item>on</item>
    </string-array>

    <!-- Camera Preferences Wavelet Denoise dialog box entries -->
    <string-array name="pref_camera_denoise_entryvalues" translatable="false">
        <item>denoise-off</item>
        <item>denoise-on</item>
    </string-array>

    <string-array name="pref_camera_denoise_entries" translatable="false">
    <item>@string/pref_camera_denoise_entry_off</item>
    <item>@string/pref_camera_denoise_entry_on</item>
    </string-array>

    <!-- Camera Preferences Auto Scene Detection dialog box entries -->
    <string-array name="pref_camera_scenedetect_entries" translatable="false">
        <item>@string/pref_camera_scenedetect_entry_off</item>
        <item>@string/pref_camera_scenedetect_entry_on</item>
    </string-array>
    <string-array name="pref_camera_scenedetect_entryvalues" translatable="false">
        <item>off</item>
        <item>on</item>
    </string-array>

    <!-- Camera Preferences Auto Exposure dialog box entries -->
     <string-array name="pref_camera_autoexposure_entries">
         <item>@string/pref_camera_autoexposure_entry_frameaverage</item>
         <item>@string/pref_camera_autoexposure_entry_centerweighted</item>
         <item>@string/pref_camera_autoexposure_entry_spotmetering</item>
     </string-array>

     <!-- Do not localize entryvalues -->
     <string-array name="pref_camera_autoexposure_entryvalues">
         <item>@string/pref_camera_autoexposure_value_frameaverage</item>
         <item>@string/pref_camera_autoexposure_value_centerweighted</item>
         <item>@string/pref_camera_autoexposure_value_spotmetering</item>
     </string-array>
    <!-- Camera Preferences Video Encoder dialog box entries -->
    <string-array name="pref_camera_videoencoder_entries" translatable="false">
        <item>@string/pref_camera_videoencoder_entry_0</item>
        <item>@string/pref_camera_videoencoder_entry_1</item>
        <item>@string/pref_camera_videoencoder_entry_2</item>
        <item>@string/pref_camera_videoencoder_entry_3</item>
    </string-array>

    <string-array name="pref_camera_videoencoder_entryvalues" translatable="false">
        <item>m4v</item>
        <item>h263</item>
        <item>h264</item>
        <item>h265</item>
    </string-array>

    <!-- Camera Preferences Audio Encoder dialog box entries -->
    <string-array name="pref_camera_audioencoder_entries" translatable="false">
        <item>@string/pref_camera_audioencoder_entry_0</item>
        <item>@string/pref_camera_audioencoder_entry_1</item>
    </string-array>

    <string-array name="pref_camera_audioencoder_entryvalues" translatable="false">
        <item>amrnb</item>
        <item>aac</item>
    </string-array>

    <!-- Camera Preferences Video Duration dialog box entries -->
    <string-array name="pref_camera_video_duration_entries" translatable="false">
    <item>@string/pref_camera_video_duration_entry_mms</item>
    <item>@string/pref_camera_video_duration_entry_10</item>
    <item>@string/pref_camera_video_duration_entry_30</item>
    <item>@string/pref_camera_video_duration_entry_nolimit</item>
    </string-array>

    <!-- The numbers are in minutes, except -1 means the duration suitable for mms. -->
    <string-array name="pref_camera_video_duration_entryvalues" translatable="false">
    <item>-1</item>
    <item>10</item>
    <item>30</item>
    <item>0</item>
    </string-array>

    <!-- Camera Preferences Skin Tone Enhancement dialog box entries -->
     <string-array name="pref_camera_skinToneEnhancement_entries">
          <item>@string/pref_camera_skinToneEnhancement_entry_enable</item>
          <item>@string/pref_camera_skinToneEnhancement_entry_disable</item>
     </string-array>

     <!-- Do not localize entryvalues -->
     <string-array name="pref_camera_skinToneEnhancement_entryvalues">
          <item>@string/pref_camera_skinToneEnhancement_value_enable</item>
          <item>@string/pref_camera_skinToneEnhancement_value_disable</item>
     </string-array>

     <!-- Camera Preferences Redeye Reduction dialog box entries -->
     <string-array name="pref_camera_redeyereduction_entries" translatable="false">
         <item>@string/pref_camera_redeyereduction_entry_disable</item>
         <item>@string/pref_camera_redeyereduction_entry_enable</item>
     </string-array>

     <string-array name="pref_camera_redeyereduction_entryvalues" translatable="false">
         <item>disable</item>
         <item>enable</item>
     </string-array>

     <string-array name="pref_camera_selfiemirror_entries" translatable="false">
         <item>@string/pref_camera_selfiemirror_entry_disable</item>
         <item>@string/pref_camera_selfiemirror_entry_enable</item>
     </string-array>
     <string-array name="pref_camera_selfiemirror_entryvalues" translatable="false">
         <item>disable</item>
         <item>enable</item>
     </string-array>

     <string-array name="pref_camera_shuttersound_entries" translatable="false">
         <item>@string/pref_camera_shuttersound_entry_disable</item>
         <item>@string/pref_camera_shuttersound_entry_enable</item>
     </string-array>
     <string-array name="pref_camera_shuttersound_entryvalues" translatable="false">
         <item>disable</item>
         <item>enable</item>
     </string-array>

    <!-- Camera Preferences Selectable Zone AF dialog box entries -->
    <string-array name="pref_camera_selectablezoneaf_entries" translatable="false">
        <item>@string/pref_camera_selectablezoneaf_entry_auto</item>
        <item>@string/pref_camera_selectablezoneaf_entry_spotmetering</item>
        <item>@string/pref_camera_selectablezoneaf_entry_centerweighted</item>
        <item>@string/pref_camera_selectablezoneaf_entry_frameaverage</item>
    </string-array>

    <string-array name="pref_camera_selectablezoneaf_entryvalues" translatable="false">
         <item>@string/pref_camera_selectablezoneaf_value_auto</item>
         <item>@string/pref_camera_selectablezoneaf_value_spotmetering</item>
         <item>@string/pref_camera_selectablezoneaf_value_centerweighted</item>
         <item>@string/pref_camera_selectablezoneaf_value_frameaverage</item>
    </string-array>


    <string-array name="pref_camera_manual_exp_entries" translatable="false">
        <item>@string/pref_camera_manual_exp_entry_ISO_priority</item>
        <item>@string/pref_camera_manual_exp_entry_exptime_priority</item>
        <item>@string/pref_camera_manual_exp_entry_user_setting</item>
        <item>@string/pref_camera_manual_exp_entry_gains_priority</item>
        <item>@string/pref_camera_manual_exp_entry_off</item>
    </string-array>

    <string-array name="pref_camera_manual_exp_entry_values" translatable="false">
         <item>@string/pref_camera_manual_exp_value_ISO_priority</item>
         <item>@string/pref_camera_manual_exp_value_exptime_priority</item>
         <item>@string/pref_camera_manual_exp_value_user_setting</item>
        <item>@string/pref_camera_manual_exp_value_gains_priority</item>
         <item>@string/pref_camera_manual_exp_value_off</item>
    </string-array>

    <string-array name="pref_camera_manual_wb_entries" translatable="false">
        <item>@string/pref_camera_manual_wb_entry_color_temperature</item>
        <item>@string/pref_camera_manual_wb_entry_rbgb_gains</item>
        <item>@string/pref_camera_manual_wb_entry_off</item>
    </string-array>

    <string-array name="pref_camera_manual_wb_entry_values" translatable="false">
         <item>@string/pref_camera_manual_wb_value_color_temperature</item>
         <item>@string/pref_camera_manual_wb_value_rbgb_gains</item>
         <item>@string/pref_camera_manual_wb_value_off</item>
    </string-array>

    <string-array name="pref_camera_manual_focus_entries" translatable="false">
        <item>@string/pref_camera_manual_focus_entry_scale_mode</item>
        <item>@string/pref_camera_manual_focus_entry_diopter_mode</item>
        <item>@string/pref_camera_manual_focus_entry_off</item>
    </string-array>

    <string-array name="pref_camera_manual_focus_entry_values" translatable="false">
         <item>@string/pref_camera_manual_focus_value_scale_mode</item>
         <item>@string/pref_camera_manual_focus_value_diopter_mode</item>
         <item>@string/pref_camera_manual_focus_value_off</item>
         </string-array>

    <!-- ZSL dialog box entries -->
    <string-array name="pref_camera_zsl_entries" translatable="false">
    <item>@string/pref_camera_zsl_entry_off</item>
    <item>@string/pref_camera_zsl_entry_on</item>
    </string-array>

    <string-array name="pref_camera_zsl_entryvalues" translatable="false">
    <item>@string/pref_camera_zsl_value_off</item>
    <item>@string/pref_camera_zsl_value_on</item>
    </string-array>

    <!-- Video HDR dialog box entries -->
    <string-array name="pref_camera_video_hdr_entries" translatable="false">
        <item>@string/pref_camera_video_hdr_entry_off</item>
        <item>@string/pref_camera_video_hdr_entry_on</item>
    </string-array>

    <string-array name="pref_camera_video_hdr_entryvalues" translatable="false">
        <item>@string/pref_camera_video_hdr_value_off</item>
        <item>@string/pref_camera_video_hdr_value_on</item>
    </string-array>

    <!-- Camera Preferences Selectable Advanced features dialog box entries -->
    <string-array name="pref_camera_advanced_features_entries" translatable="false">
        <item>@string/pref_camera_advanced_feature_entry_none</item>
        <item>@string/pref_camera_advanced_feature_entry_ubifocus</item>
        <item>@string/pref_camera_advanced_feature_entry_refocus</item>
        <item>@string/pref_camera_advanced_feature_entry_chromaflash</item>
        <item>@string/pref_camera_advanced_feature_entry_optizoom</item>
        <item>@string/pref_camera_advanced_feature_entry_FSSR</item>
        <item>@string/pref_camera_advanced_feature_entry_trueportrait</item>
        <item>@string/pref_camera_advanced_feature_entry_multi_touch_focus</item>
        <item>@string/pref_camera_advanced_feature_entry_stillmore</item>
    </string-array>

    <string-array name="pref_camera_advanced_features_entryvalues" translatable="false">
        <item>@string/pref_camera_advanced_feature_value_none</item>
        <item>@string/pref_camera_advanced_feature_value_ubifocus_on</item>
        <item>@string/pref_camera_advanced_feature_value_refocus_on</item>
        <item>@string/pref_camera_advanced_feature_value_chromaflash_on</item>
        <item>@string/pref_camera_advanced_feature_value_optizoom_on</item>
        <item>@string/pref_camera_advanced_feature_value_FSSR_on</item>
        <item>@string/pref_camera_advanced_feature_value_trueportrait_on</item>
        <item>@string/pref_camera_advanced_feature_value_multi_touch_focus_on</item>
        <item>@string/pref_camera_advanced_feature_value_stillmore_on</item>
    </string-array>

    <!-- Camera Preferences Long Shot dialog box entries -->
    <string-array name="pref_camera_longshot_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera_longshot_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <!-- AUTO HDR  dialog box entries -->
    <string-array name="pref_camera_auto_hdr_entries" translatable="false">
    <item>@string/pref_camera_auto_hdr_entry_enable</item>
    <item>@string/pref_camera_auto_hdr_entry_disable</item>
    </string-array>

    <string-array name="pref_camera_auto_hdr_entryvalues" translatable="false">
    <item>@string/pref_camera_auto_hdr_value_enable</item>
    <item>@string/pref_camera_auto_hdr_value_disable</item>
    </string-array>

    <!-- CDS  dialog box entries -->
    <string-array name="pref_camera_cds_entries" translatable="false">
    <item>@string/pref_camera_cds_entry_off</item>
    <item>@string/pref_camera_cds_entry_on</item>
    <item>@string/pref_camera_cds_entry_auto</item>
    </string-array>

    <string-array name="pref_camera_cds_entryvalues" translatable="false">
    <item>@string/pref_camera_cds_value_off</item>
    <item>@string/pref_camera_cds_value_on</item>
    <item>@string/pref_camera_cds_value_auto</item>
    </string-array>

    <!-- video CDS  dialog box entries -->
    <string-array name="pref_camera_video_cds_entries" translatable="false">
    <item>@string/pref_camera_video_cds_entry_off</item>
    <item>@string/pref_camera_video_cds_entry_on</item>
    <item>@string/pref_camera_video_cds_entry_auto</item>
    </string-array>

    <string-array name="pref_camera_video_cds_entryvalues" translatable="false">
    <item>@string/pref_camera_video_cds_value_off</item>
    <item>@string/pref_camera_video_cds_value_on</item>
    <item>@string/pref_camera_video_cds_value_auto</item>
    </string-array>

    <!-- TNR  dialog box entries -->
    <string-array name="pref_camera_tnr_entries" translatable="false">
    <item>@string/pref_camera_tnr_entry_disable</item>
    <item>@string/pref_camera_tnr_entry_enable</item>
    </string-array>

    <string-array name="pref_camera_tnr_entryvalues" translatable="false">
    <item>@string/pref_camera_tnr_value_off</item>
    <item>@string/pref_camera_tnr_value_on</item>
    </string-array>

    <!-- video TNR  dialog box entries -->
    <string-array name="pref_camera_video_tnr_entries" translatable="false">
    <item>@string/pref_camera_video_tnr_entry_disable</item>
    <item>@string/pref_camera_video_tnr_entry_enable</item>
    </string-array>

    <string-array name="pref_camera_video_tnr_entryvalues" translatable="false">
    <item>@string/pref_camera_video_tnr_value_off</item>
    <item>@string/pref_camera_video_tnr_value_on</item>
    </string-array>

    <!-- Camera Preferences Selectable HDR modes dialog box entries -->
    <string-array name="pref_camera_hdr_mode_entries" translatable="false">
        <item>@string/pref_camera_hdr_mode_entry_sensor</item>
        <item>@string/pref_camera_hdr_mode_entry_multi_frame</item>
    </string-array>

    <string-array name="pref_camera_hdr_mode_entryvalues" translatable="false">
        <item>@string/pref_hdr_mode_value_sensor</item>
        <item>@string/pref_hdr_mode_value_multi_frame</item>
    </string-array>

    <!-- Camera Preferences Selectable HDR need 1x frame -->
    <string-array name="pref_camera_hdr_need_1x_entries" translatable="false">
        <item>@string/pref_camera_hdr_need_1x_entry_false</item>
        <item>@string/pref_camera_hdr_need_1x_entry_true</item>
    </string-array>

    <string-array name="pref_camera_hdr_need_1x_entryvalues" translatable="false">
        <item>@string/pref_hdr_need_1x_value_false</item>
        <item>@string/pref_hdr_need_1x_value_true</item>
    </string-array>
        <string-array name="pref_camera_tsmakeup_entries">
        <item>@string/pref_camera_tsmakeup_entry_off</item>
        <item>@string/pref_camera_tsmakeup_entry_on</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera_tsmakeup_entryvalues">
        <item>Off</item>
        <item>On</item>
    </string-array>

    <string-array name="pref_camera_filter_mode_entries" translatable="false">
        <item>@string/pref_camera_filter_mode_entry_off</item>
        <item>@string/pref_camera_filter_mode_entry_on</item>
    </string-array>
    <string-array name="pref_camera_filter_mode_entryvalues" translatable="false">
        <item>Off</item>
        <item>On</item>
    </string-array>

    <!-- Camera Preferences Color effect dialog box entries -->
    <string-array name="pref_camera_tsmakeup_level_entries" translatable="false">
        <item>@string/pref_camera_tsmakeup_entry_off</item>
        <item>@string/pref_camera_tsmakeup_entry_low</item>
        <item>@string/pref_camera_tsmakeup_entry_mid</item>
        <item>@string/pref_camera_tsmakeup_entry_high</item>
        <item>@string/pref_camera_tsmakeup_custom</item>
    </string-array>

    <array name="tsmakeup_level_thumbnails" translatable="false">
        <item>@drawable/ic_ts_makeup_level_off_selector</item>
        <item>@drawable/ic_ts_makeup_level_1_selector</item>
        <item>@drawable/ic_ts_makeup_level_2_selector</item>
        <item>@drawable/ic_ts_makeup_level_3_selector</item>
        <item>@drawable/ic_ts_makeup_custom_selector</item>
    </array>

    <string-array name="pref_camera_tsmakeup_level_entryvalues" translatable="false">
        <item>@string/pref_camera_tsmakeup_default</item>
        <item>20</item>
        <item>60</item>
        <item>100</item>
        <item>none</item>
    </string-array>
    <string-array name="pref_ts_makeup_icons" translatable="false">
        <item>@drawable/ic_ts_makeup_off</item>
        <item>@drawable/ic_ts_makeup_on</item>
    </string-array>
    <string-array name="pref_filter_mode_icons" translatable="false">
        <item>@drawable/ic_settings_filter</item>
        <item>@drawable/ic_settings_filter_on</item>
    </string-array>

    <string-array name="pref_camera_instant_capture_entries" translatable="true">
        <item>@string/pref_camera_instant_capture_entry_aggressive_aec</item>
        <item>@string/pref_camera_instant_capture_entry_fast_aec</item>
        <item>@string/pref_camera_instant_capture_entry_disable</item>
    </string-array>

    <string-array name="pref_camera_instant_capture_entry_values" translatable="false">
        <item>@string/pref_camera_instant_capture_value_aggressive_aec</item>
        <item>@string/pref_camera_instant_capture_value_fast_aec</item>
        <item>@string/pref_camera_instant_capture_value_disable</item>
    </string-array>

    <string-array name="pref_camera_bokeh_mode_entries" translatable="true">
        <item>@string/pref_camera_bokeh_mode_entry_enable</item>
        <item>@string/pref_camera_bokeh_mode_entry_disable</item>
    </string-array>

    <string-array name="pref_camera_bokeh_mode_entry_values" translatable="false">
        <item>@string/pref_camera_bokeh_mode_entry_value_enable</item>
        <item>@string/pref_camera_bokeh_mode_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera_bokeh_mpo_entries" translatable="true">
        <item>@string/pref_camera_bokeh_mpo_entry_enable</item>
        <item>@string/pref_camera_bokeh_mpo_entry_disable</item>
    </string-array>

    <string-array name="pref_camera_bokeh_mpo_entry_values" translatable="false">
        <item>@string/pref_camera_bokeh_mpo_entry_value_enable</item>
        <item>@string/pref_camera_bokeh_mpo_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera2_stats_visualizer_default" translatable="false">
        <item></item>
    </string-array>

    <string-array name="pref_camera_bokeh_blur_degree_entries" translatable="true">
        <item>0</item>
        <item>10</item>
        <item>20</item>
        <item>30</item>
        <item>40</item>
        <item>50</item>
        <item>60</item>
        <item>70</item>
        <item>80</item>
        <item>90</item>
        <item>100</item>
    </string-array>

    <string-array name="pref_camera_bokeh_blur_degree_entry_values" translatable="false">
        <item>0</item>
        <item>10</item>
        <item>20</item>
        <item>30</item>
        <item>40</item>
        <item>50</item>
        <item>60</item>
        <item>70</item>
        <item>80</item>
        <item>90</item>
        <item>100</item>
    </string-array>
    <!-- Camera Preferences zoom dialog box entries -->
    <string-array name="pref_camera_zoom_entries" translatable="false">
        <item>@string/pref_camera_zoom_entry_off</item>
        <item>@string/pref_camera_zoom_entry_1x</item>
        <item>@string/pref_camera_zoom_entry_2x</item>
        <item>@string/pref_camera_zoom_entry_3x</item>
        <item>@string/pref_camera_zoom_entry_4x</item>
        <item>@string/pref_camera_zoom_entry_5x</item>
        <item>@string/pref_camera_zoom_entry_6x</item>
        <item>@string/pref_camera_zoom_entry_7x</item>
        <item>@string/pref_camera_zoom_entry_8x</item>
        <item>@string/pref_camera_zoom_entry_9x</item>
        <item>@string/pref_camera_zoom_entry_10x</item>
    </string-array>

    <string-array name="pref_camera_zoom_entryvalues" translatable="false">
        <item>@string/pref_camera_zoom_default</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
    </string-array>

    <string-array name="pref_camera_tone_mapping_entries" translatable="false">
        <!--item>@string/pref_camera_tone_mapping_entry_dark_boost_offset</item>
        <item>@string/pref_camera_tone_mapping_entry_fourth_tone_anchor</item -->
		<item>@string/pref_camera_tone_mapping_entry_user_setting</item>
        <item>@string/pref_camera_tone_mapping_entry_off</item>
    </string-array>
    <string-array name="pref_camera_tone_mapping_entry_values" translatable="false">
        <!--item>@string/pref_camera_tone_mapping_value_dark_boost_offset</item>
        <item>@string/pref_camera_tone_mapping_value_fourth_tone_anchor</item -->
        <item>@string/pref_camera_tone_mapping_value_user_setting</item>
        <item>@string/pref_camera_tone_mapping_value_off</item>
    </string-array>

    <string-array name="pref_camera2_live_preview_entries" translatable="false">
        <item>@string/pref_camera2_live_preview_entry_disable</item>
        <item>@string/pref_camera2_live_preview_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_live_preview_entryvalues" translatable="false">
        <item>@string/pref_camera2_live_preview_entry_value_disable</item>
        <item>@string/pref_camera2_live_preview_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_gridline_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera2_gridline_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>
</resources>

