<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2013, The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <dimen name="hint_y_offset">64dp</dimen>
    <dimen name="pano_mosaic_surface_height">240dp</dimen>
    <dimen name="pano_review_button_width">70dp</dimen>
    <dimen name="pano_review_button_height">45dp</dimen>
    <dimen name="setting_popup_right_margin">5dp</dimen>
    <dimen name="setting_row_height">50dp</dimen>
    <dimen name="setting_item_text_size">18sp</dimen>
    <dimen name="setting_knob_width">20dp</dimen>
    <dimen name="setting_knob_text_size">20dp</dimen>
    <dimen name="setting_item_text_width">95dp</dimen>
    <dimen name="setting_popup_window_width">240dp</dimen>
    <dimen name="setting_item_list_margin">2dp</dimen>
    <dimen name="indicator_bar_width">48dp</dimen>
    <dimen name="popup_title_text_size">22dp</dimen>
    <dimen name="popup_title_frame_min_height">49dp</dimen>
    <dimen name="big_setting_popup_window_width">320dp</dimen>
    <dimen name="setting_item_icon_width">28dp</dimen>
    <dimen name="effect_setting_item_icon_width">40dp</dimen>
    <dimen name="effect_setting_item_text_size">12sp</dimen>
    <dimen name="effect_setting_type_text_size">12sp</dimen>
    <dimen name="effect_setting_type_text_min_height">36dp</dimen>
    <dimen name="effect_setting_clear_text_size">20dp</dimen>
    <dimen name="effect_setting_clear_text_min_height">45dp</dimen>
    <dimen name="effect_setting_type_text_left_padding">16dp</dimen>
    <dimen name="onscreen_indicators_height">28dp</dimen>
    <dimen name="onscreen_exposure_indicator_text_size">15dp</dimen>
    <dimen name="switch_padding">16dp</dimen>
    <dimen name="switch_min_width">96dp</dimen>
    <dimen name="switch_text_max_width">44dp</dimen>
    <dimen name="thumb_text_padding">12dp</dimen>
    <dimen name="thumb_text_size">14sp</dimen>
    <dimen name="setting_popup_right_margin_large">8dp</dimen>
    <dimen name="setting_row_height_large">54dp</dimen>
    <dimen name="setting_popup_window_width_large">260dp</dimen>
    <dimen name="indicator_bar_width_large">72dp</dimen>
    <dimen name="setting_item_icon_width_large">48dp</dimen>
    <dimen name="onscreen_indicators_height_large">36dp</dimen>
    <dimen name="pano_mosaic_surface_height_xlarge">480dp</dimen>
    <dimen name="pano_review_button_width_xlarge">180dp</dimen>
    <dimen name="pano_review_button_height_xlarge">115dp</dimen>
    <dimen name="setting_row_height_xlarge">50dp</dimen>
    <dimen name="setting_item_text_size_xlarge">21dp</dimen>
    <dimen name="setting_knob_width_xlarge">50dp</dimen>
    <dimen name="setting_item_text_width_xlarge">130dp</dimen>
    <dimen name="setting_popup_window_width_xlarge">410dp</dimen>
    <dimen name="setting_item_list_margin_xlarge">24dp</dimen>
    <dimen name="indicator_bar_width_xlarge">13dp</dimen>
    <dimen name="popup_title_text_size_xlarge">22dp</dimen>
    <dimen name="popup_title_frame_min_height_xlarge">60dp</dimen>
    <dimen name="big_setting_popup_window_width_xlarge">590dp</dimen>
    <dimen name="setting_item_icon_width_xlarge">35dp</dimen>
    <dimen name="effect_setting_item_icon_width_xlarge">54dp</dimen>
    <dimen name="effect_setting_item_text_size_xlarge">21dp</dimen>
    <dimen name="effect_setting_type_text_size_xlarge">21dp</dimen>
    <dimen name="effect_setting_type_text_min_height_xlarge">34dp</dimen>
    <dimen name="effect_setting_clear_text_size_xlarge">23dp</dimen>
    <dimen name="effect_setting_clear_text_min_height_xlarge">44dp</dimen>
    <dimen name="effect_setting_type_text_left_padding_xlarge">26dp</dimen>
    <dimen name="onscreen_indicators_height_xlarge">36dp</dimen>
    <dimen name="onscreen_exposure_indicator_text_size_xlarge">18dp</dimen>
    <dimen name="pie_progress_radius">25dp</dimen>
    <dimen name="pie_progress_width">3dp</dimen>
    <dimen name="pie_radius_start">80dp</dimen>
    <dimen name="pie_radius_increment">48dp</dimen>
    <dimen name="pie_touch_slop">12dp</dimen>
    <dimen name="pie_touch_offset">32dp</dimen>
    <dimen name="pie_view_size">48dp</dimen>
    <dimen name="pie_arc_offset">48dp</dimen>
    <dimen name="pie_item_radius">370dp</dimen>
    <dimen name="pie_arc_radius">214dp</dimen>
    <dimen name="pie_deadzone_width">36dp</dimen>
    <dimen name="pie_anglezone_width">92dp</dimen>
    <dimen name="focus_radius_offset">8dp</dimen>
    <dimen name="focus_inner_offset">24dp</dimen>
    <dimen name="focus_outer_stroke">3dp</dimen>
    <dimen name="focus_inner_stroke">2dp</dimen>
    <dimen name="zoom_ring_min">48dp</dimen>
    <dimen name="switcher_size">72dp</dimen>
    <dimen name="menu_size">52dp</dimen>
    <dimen name="menu_outer_size">80dp</dimen>
    <dimen name="mute_outer_size">80dp</dimen>
    <dimen name="toggle_size">30dp</dimen>
    <dimen name="toggle_outer_size">60dp</dimen>
    <dimen name="face_circle_stroke">2dip</dimen>
    <dimen name="face_points_stroke">4dip</dimen>
    <dimen name="zoom_font_size">28dp</dimen>
    <dimen name="shutter_offset">11dp</dimen>
    <dimen name="size_thumbnail">200dip</dimen>
    <dimen name="size_preview">400dip</dimen>
    <dimen name="navigation_bar_height">48dip</dimen>
    <dimen name="navigation_bar_width">42dip</dimen>
    <dimen name="capture_size">48dip</dimen>
    <dimen name="capture_border">8dip</dimen>
    <dimen name="capture_margin_right">16dip</dimen>
    <dimen name="capture_margin_top">16dip</dimen>
    <dimen name="camera_controls_size">0dip</dimen>
    <dimen name="camera_film_strip_gap">32dip</dimen>
    <dimen name="shutter_size">80dp</dimen>

    <dimen name="appwidget_width">180dp</dimen>
    <dimen name="appwidget_height">180dp</dimen>
    <dimen name="stack_photo_width">160dp</dimen>
    <dimen name="stack_photo_height">120dp</dimen>

    <!-- configuration for legacy album set page -->
    <integer name="albumset_rows_land">2</integer>
    <integer name="albumset_rows_port">3</integer>
    <dimen name="albumset_padding_top">7dp</dimen>
    <dimen name="albumset_padding_bottom">7dp</dimen>
    <dimen name="albumset_slot_gap">7dp</dimen>

    <dimen name="albumset_label_background_height">30dp</dimen>
    <dimen name="albumset_title_offset">10dp</dimen>
    <dimen name="albumset_count_offset">10dp</dimen>
    <dimen name="albumset_title_font_size">12sp</dimen>
    <dimen name="albumset_count_font_size">9sp</dimen>
    <dimen name="albumset_left_margin">2dp</dimen>
    <dimen name="albumset_title_right_margin">20dp</dimen>
    <dimen name="albumset_icon_size">25dp</dimen>

    <!-- configuration for album page -->
    <integer name="album_rows_land">2</integer>
    <integer name="album_rows_port">4</integer>
    <dimen name="album_slot_gap">5dp</dimen>

    <!-- configuration for manage page -->
    <dimen name="cache_pin_size">24dp</dimen>
    <dimen name="cache_pin_margin">8dp</dimen>

    <!-- for manage cache bar -->
    <dimen name="manage_cache_bottom_height">48dp</dimen>

    <!--  configuration for filtershow UI -->
    <dimen name="thumbnail_size">96dip</dimen>
    <dimen name="thumbnail_margin">3dip</dimen>
    <dimen name="action_item_height">175dip</dimen>

    <!-- configuration for album set page -->
    <dimen name="album_set_item_image_height">120dp</dimen>
    <dimen name="album_set_item_width">140dp</dimen>

    <!-- configuration for preview in editor -->
    <dimen name="photoeditor_text_size">12dp</dimen>
    <dimen name="photoeditor_text_padding">10dp</dimen>
    <dimen name="photoeditor_original_text_size">18dp</dimen>
    <dimen name="photoeditor_original_text_margin">4dp</dimen>

    <dimen name="scene_mode_height">85dp</dimen>
    <dimen name="scene_mode_width">90dp</dimen>
    <dimen name="scene_mode_padding">10dp</dimen>
    <dimen name="filter_mode_height">100dp</dimen>
    <dimen name="filter_mode_width">100dp</dimen>
    <dimen name="filter_mode_padding">0dp</dimen>

    <dimen name="remaining_photos_margin">67dp</dimen>
    <dimen name="tsmakeup_mode_paddingBottom">96dp</dimen>
    <dimen name="tsmakeup_mode_level_size">96dp</dimen>

    <!-- Margins for 4:3 preview on 16:9 screen with 640dp height -->
    <dimen name="preview_top_margin">57dp</dimen>
    <dimen name="preview_bottom_margin">103dp</dimen>

    <dimen name="refocus_circle_diameter_1">113dp</dimen>
    <dimen name="refocus_circle_diameter_2">81dp</dimen>
    <dimen name="refocus_circle_diameter_3">95dp</dimen>
    <dimen name="refocus_cross_length">19dp</dimen>
    <dimen name="refocus_stroke_width">2dp</dimen>

    <dimen name="one_ui_bottom_large">75dp</dimen>
    <dimen name="one_ui_bottom_small">55dp</dimen>
</resources>
