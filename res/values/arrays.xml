<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 -->

<resources>
    <!-- Camera Preferences Video Quality entries -->
    <string-array name="pref_video_quality_entries" translatable="false">
        <item>@string/pref_video_quality_entry_4kdci</item>
        <item>@string/pref_video_quality_entry_2160p</item>
        <item>@string/pref_video_quality_entry_qHD</item>
        <item>@string/pref_video_quality_entry_2k</item>
        <item>@string/pref_video_quality_entry_1080p</item>
        <item>@string/pref_video_quality_entry_720p</item>
        <item>@string/pref_video_quality_entry_480p</item>
        <item>@string/pref_video_quality_entry_vga</item>
        <item>@string/pref_video_quality_entry_cif</item>
        <item>@string/pref_video_quality_entry_qvga</item>
   </string-array>
    <string-array name="pref_video_quality_entryvalues" translatable="false">
        <item>4096x2160</item>
        <item>3840x2160</item>
        <item>2560x1440</item>
        <item>2048x1080</item>
        <item>1920x1080</item>
        <item>1280x720</item>
        <item>720x480</item>
        <item>640x480</item>
        <item>352x288</item>
        <item>320x240</item>
    </string-array>

    <!-- Camera Preference save path entries -->
    <string-array name="pref_camera_savepath_entries" translatable="false">
        <item>@string/pref_camera_savepath_entry_0</item>
        <item>@string/pref_camera_savepath_entry_1</item>
    </string-array>

    <string-array name="pref_camera_savepath_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <!-- These values correspond to the time interval between frame capture in millseconds
    for time lapse recording -->
    <string-array name="pref_video_time_lapse_frame_interval_entryvalues" translatable="false">
        <item>0</item>
        <item>500</item>
        <item>1000</item>
        <item>1500</item>
        <item>2000</item>
        <item>2500</item>
        <item>3000</item>
        <item>4000</item>
        <item>5000</item>
        <item>6000</item>
        <item>10000</item>
        <item>12000</item>
        <item>15000</item>
        <item>24000</item>
        <item>30000</item>
        <item>60000</item>
        <item>90000</item>
        <item>120000</item>
        <item>150000</item>
        <item>180000</item>
        <item>240000</item>
        <item>300000</item>
        <item>360000</item>
        <item>600000</item>
        <item>720000</item>
        <item>900000</item>
        <item>1440000</item>
        <item>1800000</item>
        <item>3600000</item>
        <item>5400000</item>
        <item>7200000</item>
        <item>9000000</item>
        <item>10800000</item>
        <item>14400000</item>
        <item>18000000</item>
        <item>21600000</item>
        <item>36000000</item>
        <item>43200000</item>
        <item>54000000</item>
        <item>86400000</item>
    </string-array>

    <!-- These values correspond to the time interval between frame capture in
    different units (i.e. seconds, minutes, hours) for time lapse recording -->
    <string-array name="pref_video_time_lapse_frame_interval_entries">
        <item>@string/pref_video_time_lapse_frame_interval_off</item>
        <item>@string/pref_video_time_lapse_frame_interval_500</item>
        <item>@string/pref_video_time_lapse_frame_interval_1000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1500</item>
        <item>@string/pref_video_time_lapse_frame_interval_2000</item>
        <item>@string/pref_video_time_lapse_frame_interval_2500</item>
        <item>@string/pref_video_time_lapse_frame_interval_3000</item>
        <item>@string/pref_video_time_lapse_frame_interval_4000</item>
        <item>@string/pref_video_time_lapse_frame_interval_5000</item>
        <item>@string/pref_video_time_lapse_frame_interval_6000</item>
        <item>@string/pref_video_time_lapse_frame_interval_10000</item>
        <item>@string/pref_video_time_lapse_frame_interval_12000</item>
        <item>@string/pref_video_time_lapse_frame_interval_15000</item>
        <item>@string/pref_video_time_lapse_frame_interval_24000</item>
        <item>@string/pref_video_time_lapse_frame_interval_30000</item>
        <item>@string/pref_video_time_lapse_frame_interval_60000</item>
        <item>@string/pref_video_time_lapse_frame_interval_90000</item>
        <item>@string/pref_video_time_lapse_frame_interval_120000</item>
        <item>@string/pref_video_time_lapse_frame_interval_150000</item>
        <item>@string/pref_video_time_lapse_frame_interval_180000</item>
        <item>@string/pref_video_time_lapse_frame_interval_240000</item>
        <item>@string/pref_video_time_lapse_frame_interval_300000</item>
        <item>@string/pref_video_time_lapse_frame_interval_360000</item>
        <item>@string/pref_video_time_lapse_frame_interval_600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_720000</item>
        <item>@string/pref_video_time_lapse_frame_interval_900000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1440000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1800000</item>
        <item>@string/pref_video_time_lapse_frame_interval_3600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_5400000</item>
        <item>@string/pref_video_time_lapse_frame_interval_7200000</item>
        <item>@string/pref_video_time_lapse_frame_interval_9000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_10800000</item>
        <item>@string/pref_video_time_lapse_frame_interval_14400000</item>
        <item>@string/pref_video_time_lapse_frame_interval_18000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_21600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_36000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_43200000</item>
        <item>@string/pref_video_time_lapse_frame_interval_54000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_86400000</item>
    </string-array>

    <!-- These values correspond to the time interval between frame capture
    for time lapse recording -->
    <string-array name="pref_video_time_lapse_frame_interval_duration_values" translatable="false">
        <item>0.5</item>
        <item>1</item>
        <item>1.5</item>
        <item>2</item>
        <item>2.5</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>10</item>
        <item>12</item>
        <item>15</item>
        <item>24</item>
    </string-array>

    <string-array name="pref_video_time_lapse_frame_interval_units">
        <item>@string/time_lapse_seconds</item>
        <item>@string/time_lapse_minutes</item>
        <item>@string/time_lapse_hours</item>
    </string-array>

    <!-- Camera Preferences Picture size dialog box entries -->
    <string-array name="pref_camera_picturesize_entries" translatable="false">
        <item>@string/pref_camera_picturesize_entry_24mp</item>
        <item>@string/pref_camera_picturesize_entry_21mp</item>
        <item>@string/pref_camera_picturesize_entry_16mp</item>
        <item>@string/pref_camera_picturesize_entry_16mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_13mp</item>
        <item>@string/pref_camera_picturesize_entry_12mp</item>
        <item>@string/pref_camera_picturesize_entry_8mp</item>
        <item>@string/pref_camera_picturesize_entry_8mp</item>
        <item>@string/pref_camera_picturesize_entry_square</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_4mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1920x1080</item>
        <item>@string/pref_camera_picturesize_entry_2mp</item>
        <item>@string/pref_camera_picturesize_entry_2mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_1_5mp</item>
        <item>@string/pref_camera_picturesize_entry_1_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1280x768</item>
        <item>@string/pref_camera_picturesize_entry_1280x720</item>
        <item>@string/pref_camera_picturesize_entry_1280x400</item>
        <item>@string/pref_camera_picturesize_entry_1mp</item>
        <item>@string/pref_camera_picturesize_entry_800x600</item>
        <item>@string/pref_camera_picturesize_entry_800x480</item>
        <item>960 x 720</item>
        <item>720 x 480</item>
        <item>@string/pref_camera_picturesize_entry_vga</item>
        <item>@string/pref_camera_picturesize_entry_352x288</item>
        <item>@string/pref_camera_picturesize_entry_qvga</item>
    </string-array>
    <!-- When launching the camera app first time, we will set the picture
         size to the first one in the list that is also supported by the
         driver -->
    <string-array name="pref_camera_picturesize_entryvalues" translatable="false">
        <item>5656x4242</item>
        <item>5344x4008</item>
        <item>4608x3456</item>
        <item>5312x2988</item>
        <item>4160x3120</item>
        <item>4000x3000</item>
        <item>3840x2160</item>
        <item>3264x2448</item>
        <item>2976x2976</item>
        <item>2592x1944</item>
        <item>2592x1936</item>
        <item>2560x1920</item>
        <item>2688x1512</item>
        <item>2048x1536</item>
        <item>2048x1520</item>
        <item>1920x1080</item>
        <item>1600x1200</item>
        <item>1920x1088</item>
        <item>1440x1080</item>
        <item>1280x960</item>
        <item>1280x768</item>
        <item>1280x720</item>
        <item>1280x400</item>
        <item>1024x768</item>
        <item>800x600</item>
        <item>800x480</item>
        <item>960x720</item>
        <item>720x480</item>
        <item>640x480</item>
        <item>352x288</item>
        <item>320x240</item>
    </string-array>

    <!-- Camera Preferences video snapshot size dialog box entries -->
    <string-array name="pref_camera_video_snapsize_entries" translatable="false">
        <item>Auto</item>
        <item>@string/pref_camera_picturesize_entry_21mp</item>
        <item>@string/pref_camera_picturesize_entry_16mp</item>
        <item>@string/pref_camera_picturesize_entry_16mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_13mp</item>
        <item>@string/pref_camera_picturesize_entry_12mp</item>
        <item>@string/pref_camera_picturesize_entry_8mp</item>
        <item>@string/pref_camera_picturesize_entry_8mp</item>
        <item>@string/pref_camera_picturesize_entry_square</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_4mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1920x1080</item>
        <item>@string/pref_camera_picturesize_entry_2mp</item>
        <item>@string/pref_camera_picturesize_entry_2mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_1_5mp</item>
        <item>@string/pref_camera_picturesize_entry_1_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1280x768</item>
        <item>@string/pref_camera_picturesize_entry_1280x720</item>
        <item>@string/pref_camera_picturesize_entry_1mp</item>
        <item>@string/pref_camera_picturesize_entry_800x600</item>
        <item>@string/pref_camera_picturesize_entry_800x480</item>
        <item>960 x 720</item>
        <item>720 x 480</item>
        <item>@string/pref_camera_picturesize_entry_vga</item>
        <item>@string/pref_camera_picturesize_entry_352x288</item>
        <item>@string/pref_camera_picturesize_entry_qvga</item>
    </string-array>

    <!-- default is auto, which calculates largest snapshot
         size with same aspect ratio as preview. -->
    <string-array name="pref_camera_video_snapsize_entryvalues" translatable="false">
        <item>auto</item>
        <item>5344x4008</item>
        <item>4608x3456</item>
        <item>5312x2988</item>
        <item>4160x3120</item>
        <item>4000x3000</item>
        <item>3840x2160</item>
        <item>3264x2448</item>
        <item>2976x2976</item>
        <item>2592x1944</item>
        <item>2592x1936</item>
        <item>2560x1920</item>
        <item>2688x1512</item>
        <item>2048x1536</item>
        <item>2048x1520</item>
        <item>1920x1080</item>
        <item>1600x1200</item>
        <item>1920x1088</item>
        <item>1440x1080</item>
        <item>1280x960</item>
        <item>1280x768</item>
        <item>1280x720</item>
        <item>1024x768</item>
        <item>800x600</item>
        <item>800x480</item>
        <item>960x720</item>
        <item>720x480</item>
        <item>640x480</item>
        <item>352x288</item>
        <item>320x240</item>
    </string-array>

    <!-- Camera Preferences focus mode dialog box entries -->
    <string-array name="pref_camera_focusmode_entries" translatable="false">
        <item>@string/pref_camera_focusmode_entry_auto</item>
        <item>@string/pref_camera_focusmode_entry_infinity</item>
        <item>@string/pref_camera_focusmode_entry_macro</item>
        <item>@string/pref_camera_focusmode_entry_normal</item>
        <item>@string/pref_camera_focusmode_entry_continuous</item>
    </string-array>

    <string-array name="pref_camera_focusmode_entryvalues" translatable="false">
        <item>auto</item>
        <item>infinity</item>
        <item>macro</item>
        <item>normal</item>
        <item>continuous-picture</item>
    </string-array>

    <string-array name="pref_camera_focusmode_labels" translatable="false">
        <item>@string/pref_camera_focusmode_label_auto</item>
        <item>@string/pref_camera_focusmode_label_infinity</item>
        <item>@string/pref_camera_focusmode_label_macro</item>
    </string-array>

    <!-- Camera Preferences flash mode dialog box entries -->
    <string-array name="pref_camera_flashmode_entries" translatable="false">
        <item>@string/pref_camera_flashmode_entry_off</item>
        <item>@string/pref_camera_flashmode_entry_auto</item>
        <item>@string/pref_camera_flashmode_entry_on</item>
    </string-array>

    <string-array name="pref_camera_flashmode_labels" translatable="false">
        <item>@string/pref_camera_flashmode_label_off</item>
        <item>@string/pref_camera_flashmode_label_auto</item>
        <item>@string/pref_camera_flashmode_label_on</item>
    </string-array>

    <string-array name="pref_camera_flashmode_entryvalues" translatable="false">
        <item>off</item>
        <item>auto</item>
        <item>on</item>
    </string-array>

    <array name="camera_flashmode_icons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_auto_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <array name="camera_flashmode_largeicons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_auto_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <!-- Videocamera Preferences flash mode dialog box entries -->
    <string-array name="pref_camera_video_flashmode_entries" translatable="false">
        <item>@string/pref_camera_flashmode_entry_off</item>
        <item>@string/pref_camera_flashmode_entry_on</item>
    </string-array>

    <string-array name="pref_camera_video_flashmode_labels" translatable="false">
        <item>@string/pref_camera_flashmode_label_off</item>
        <item>@string/pref_camera_flashmode_label_on</item>
    </string-array>

    <string-array name="pref_camera_video_flashmode_entryvalues" translatable="false">
        <item>off</item>
        <item>torch</item>
    </string-array>

    <array name="video_flashmode_icons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <array name="video_flashmode_largeicons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <string-array name="pref_camera_recordlocation_entryvalues" translatable="false">
        <item>off</item>
        <item>on</item>
    </string-array>

    <array name="pref_camera_recordlocation_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </array>

    <array name="pref_camera_recordlocation_labels" translatable="false">
        <item>@string/pref_camera_location_label</item>
        <item>@string/pref_camera_location_label</item>
    </array>

    <array name="camera_recordlocation_icons" translatable="false">
        <item>@drawable/ic_location_off</item>
        <item>@drawable/ic_location</item>
    </array>

    <array name="camera_recordlocation_largeicons" translatable="false">
        <item>@drawable/ic_location_off</item>
        <item>@drawable/ic_location</item>
    </array>

    <!-- Camera Preferences White Balance dialog box entries -->
    <string-array name="pref_camera_whitebalance_entries" translatable="false">
        <item>@string/pref_camera_whitebalance_entry_incandescent</item>
        <item>@string/pref_camera_whitebalance_entry_fluorescent</item>
        <item>@string/pref_camera_whitebalance_entry_auto</item>
        <item>@string/pref_camera_whitebalance_entry_daylight</item>
        <item>@string/pref_camera_whitebalance_entry_cloudy</item>
    </string-array>

    <string-array name="pref_camera_whitebalance_labels" translatable="false">
        <item>@string/pref_camera_whitebalance_label_incandescent</item>
        <item>@string/pref_camera_whitebalance_label_fluorescent</item>
        <item>@string/pref_camera_whitebalance_label_auto</item>
        <item>@string/pref_camera_whitebalance_label_daylight</item>
        <item>@string/pref_camera_whitebalance_label_cloudy</item>
    </string-array>

    <string-array name="pref_camera_whitebalance_entryvalues" translatable="false">
        <item>incandescent</item>
        <item>fluorescent</item>
        <item>auto</item>
        <item>daylight</item>
        <item>cloudy-daylight</item>
    </string-array>

    <array name="whitebalance_icons" translatable="false">
        <item>@drawable/ic_wb_incandescent</item>
        <item>@drawable/ic_wb_fluorescent</item>
        <item>@drawable/ic_wb_auto</item>
        <item>@drawable/ic_wb_sunlight</item>
        <item>@drawable/ic_wb_cloudy</item>
    </array>

    <array name="whitebalance_largeicons" translatable="false">
        <item>@drawable/ic_wb_incandescent</item>
        <item>@drawable/ic_wb_fluorescent</item>
        <item>@drawable/ic_wb_auto</item>
        <item>@drawable/ic_wb_sunlight</item>
        <item>@drawable/ic_wb_cloudy</item>
    </array>

    <array name="camera_wb_indicators" translatable="false">
        <item>@drawable/ic_indicator_wb_tungsten</item>
        <item>@drawable/ic_indicator_wb_fluorescent</item>
        <item>@drawable/ic_indicator_wb_off</item>
        <item>@drawable/ic_indicator_wb_daylight</item>
        <item>@drawable/ic_indicator_wb_cloudy</item>
    </array>

    <string-array name="pref_camera_chromaflash_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <array name="pref_camera_chromaflash_entryvalues" translatable="false">
        <item>@string/pref_camera_advanced_feature_value_chromaflash_off</item>
        <item>@string/pref_camera_advanced_feature_value_chromaflash_on</item>
    </array>

    <!-- Camera Preferences Scene Mode dialog box entries -->
    <string-array name="pref_camera_scenemode_entries" translatable="false">
        <item>@string/pref_camera_scenemode_entry_auto</item>
        <item>@string/pref_camera_scenemode_entry_hdr</item>
        <item>@string/pref_camera_scenemode_entry_refocus</item>
        <item>@string/pref_camera_scenemode_entry_optizoom</item>
        <item>@string/pref_camera_scenemode_entry_portrait</item>
        <item>@string/pref_camera_scenemode_entry_landscape</item>
        <item>@string/pref_camera_scenemode_entry_sports</item>
        <item>@string/pref_camera_scenemode_entry_flowers</item>
        <item>@string/pref_camera_scenemode_entry_backlight</item>
        <item>@string/pref_camera_scenemode_entry_candlelight</item>
        <item>@string/pref_camera_scenemode_entry_sunset</item>
        <item>@string/pref_camera_scenemode_entry_night</item>
        <item>@string/pref_camera_scenemode_entry_beach</item>
        <item>@string/pref_camera_scenemode_entry_snow</item>
        <item>@string/pref_camera_scenemode_entry_asd</item>
    </string-array>

    <array name="scenemode_thumbnails" translatable="false">
        <item>@drawable/ic_scene_mode_auto</item>
        <item>@drawable/ic_scene_mode_hdr</item>
        <item>@drawable/ic_scene_mode_refocus</item>
        <item>@drawable/ic_scene_mode_optizoom</item>
        <item>@drawable/ic_scene_mode_portrait</item>
        <item>@drawable/ic_scene_mode_landscape</item>
        <item>@drawable/ic_scene_mode_sports</item>
        <item>@drawable/ic_scene_mode_flower</item>
        <item>@drawable/ic_scene_mode_backlight</item>
        <item>@drawable/ic_scene_mode_candlelight</item>
        <item>@drawable/ic_scene_mode_sunset</item>
        <item>@drawable/ic_scene_mode_night</item>
        <item>@drawable/ic_scene_mode_beach</item>
        <item>@drawable/ic_scene_mode_snow</item>
        <item>@drawable/ic_scene_mode_smartauto</item>
    </array>

    <string-array name="pref_camera_scenemode_labels">
        <item>@string/pref_camera_scenemode_label_action</item>
        <item>@string/pref_camera_scenemode_label_night</item>
        <item>@string/pref_camera_scenemode_label_auto</item>
        <item>@string/pref_camera_scenemode_label_sunset</item>
        <item>@string/pref_camera_scenemode_label_party</item>
    </string-array>

    <array name="pref_camera_scenemode_icons">
        <item>@drawable/ic_sce_action</item>
        <item>@drawable/ic_sce_night</item>
        <item>@drawable/ic_sce_off</item>
        <item>@drawable/ic_sce_sunset</item>
        <item>@drawable/ic_sce_party</item>
    </array>

    <string-array name="pref_camera_scenemode_entryvalues" translatable="false">
        <item>auto</item>
        <item>hdr</item>
        <item>@string/pref_camera_advanced_feature_value_refocus_on</item>
        <item>@string/pref_camera_advanced_feature_value_optizoom_on</item>
        <item>portrait</item>
        <item>landscape</item>
        <item>sports</item>
        <item>flowers</item>
        <item>backlight</item>
        <item>candlelight</item>
        <item>sunset</item>
        <item>night</item>
        <item>beach</item>
        <item>snow</item>
        <item>asd</item>
    </string-array>

    <array name="camera_id_entries" translatable="false">
        <item>@string/pref_camera_id_entry_back</item>
        <item>@string/pref_camera_id_entry_front</item>
    </array>

    <array name="camera_id_labels" translatable="false">
        <item>@string/pref_camera_id_label_back</item>
        <item>@string/pref_camera_id_label_front</item>
    </array>

    <array name="camera_id_icons" translatable="false">
        <item>@drawable/ic_switch_back</item>
        <item>@drawable/ic_switch_front</item>
    </array>

    <array name="camera_id_largeicons" translatable="false">
        <item>@drawable/ic_switch_back</item>
        <item>@drawable/ic_switch_front</item>
    </array>

    <string-array name="pref_video_effect_entries" translatable="false">
        <item>@string/effect_none</item>
        <item>@string/effect_goofy_face_squeeze</item>
        <item>@string/effect_goofy_face_big_eyes</item>
        <item>@string/effect_goofy_face_big_mouth</item>
        <item>@string/effect_goofy_face_small_mouth</item>
        <item>@string/effect_goofy_face_big_nose</item>
        <item>@string/effect_goofy_face_small_eyes</item>
        <item>@string/effect_backdropper_space</item>
        <item>@string/effect_backdropper_sunset</item>
        <item>@string/effect_backdropper_gallery</item>
    </string-array>

    <string-array name="pref_video_effect_entryvalues" translatable="false">
        <item>@string/pref_video_effect_default</item>
        <item>goofy_face/squeeze</item>
        <item>goofy_face/big_eyes</item>
        <item>goofy_face/big_mouth</item>
        <item>goofy_face/small_mouth</item>
        <item>goofy_face/big_nose</item>
        <item>goofy_face/small_eyes</item>
        <item>backdropper/file:///system/media/video/AndroidInSpace.480p.mp4</item>
        <item>backdropper/file:///system/media/video/Sunset.480p.mp4</item>
        <item>backdropper/gallery</item>
    </string-array>

    <array name="video_effect_icons" translatable="false">
        <item>@drawable/ic_effects_holo_light</item>
        <item>@drawable/ic_video_effects_faces_squeeze_holo_dark</item>
        <item>@drawable/ic_video_effects_faces_big_eyes_holo_dark</item>
        <item>@drawable/ic_video_effects_faces_big_mouth_holo_dark</item>
        <item>@drawable/ic_video_effects_faces_small_mouth_holo_dark</item>
        <item>@drawable/ic_video_effects_faces_big_nose_holo_dark</item>
        <item>@drawable/ic_video_effects_faces_small_eyes_holo_dark</item>
        <item>@drawable/ic_video_effects_background_intergalactic_holo</item>
        <item>@drawable/ic_video_effects_background_fields_of_wheat_holo</item>
        <item>@drawable/ic_video_effects_background_normal_holo_dark</item>
    </array>

    <string-array name="pref_camera_hdr_plus_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera_hdr_plus_labels" translatable="false">
        <item>@string/pref_camera_scenemode_entry_turn_hdr_plus_on</item>
        <item>@string/pref_camera_scenemode_entry_turn_hdr_plus_off</item>
    </string-array>

    <string-array name="pref_camera_hdr_plus_icons" translatable="false">
        <item>@drawable/ic_hdr_plus_disabled</item>
        <item>@drawable/ic_hdr_plus_normal</item>
    </string-array>

    <string-array name="pref_camera_hdr_plus_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>


    <string-array name="pref_camera_hdr_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera_hdr_labels" translatable="false">
        <item>@string/pref_camera_scenemode_entry_turn_hdr_on</item>
        <item>@string/pref_camera_scenemode_entry_turn_hdr_off</item>
    </string-array>

    <string-array name="pref_camera_hdr_icons" translatable="false">
        <item>@drawable/ic_hdr_off</item>
        <item>@drawable/ic_hdr</item>
    </string-array>

    <string-array name="pref_camera_hdr_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera_timer_sound_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera_timer_sound_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <!-- Default focus mode setting.-->
    <string-array name="pref_camera_focusmode_default_array" translatable="false">
        <item>continuous-picture</item>
    </string-array>

    <!-- Icons for exposure compensation -->
    <array name="pref_camera_exposure_icons" translatable="false">
        <item>@drawable/ic_exposure_n3</item>
        <item>@drawable/ic_exposure_n2</item>
        <item>@drawable/ic_exposure_n1</item>
        <item>@drawable/ic_exposure_0</item>
        <item>@drawable/ic_exposure_p1</item>
        <item>@drawable/ic_exposure_p2</item>
        <item>@drawable/ic_exposure_p3</item>
    </array>

    <!--  Labels for Countdown timer -->
    <string-array name="pref_camera_countdown_labels">
        <item>@string/pref_camera_countdown_label_off</item>
        <item>@string/pref_camera_countdown_label_one</item>
        <item>@string/pref_camera_countdown_label_three</item>
        <item>@string/pref_camera_countdown_label_ten</item>
        <item>@string/pref_camera_countdown_label_fifteen</item>
    </string-array>

    <!-- VideoCamera Preferences video rotations entry-->
    <string-array name="pref_camera_video_rotation_entries" translatable="false">
        <item>@string/pref_camera_video_rotation_entry_0</item>
        <item>@string/pref_camera_video_rotation_entry_90</item>
        <item>@string/pref_camera_video_rotation_entry_180</item>
        <item>@string/pref_camera_video_rotation_entry_270</item>
    </string-array>

    <string-array name="pref_camera_video_rotation_labels" translatable="false">
        <item>@string/pref_camera_video_rotation_label_0</item>
        <item>@string/pref_camera_video_rotation_label_90</item>
        <item>@string/pref_camera_video_rotation_label_180</item>
        <item>@string/pref_camera_video_rotation_label_270</item>
    </string-array>

    <string-array name="pref_camera_video_rotation_entryvalues" translatable="false">
        <item>0</item>
        <item>90</item>
        <item>180</item>
        <item>270</item>
    </string-array>

</resources>
