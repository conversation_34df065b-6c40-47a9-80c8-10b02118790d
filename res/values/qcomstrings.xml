<!--
    Copyright (c) 2012-2014, 2016-2017 The Linux Foundation. All rights reserved.

    Not a Contribution.

    Copyright (C) 2007 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<resources>
    <!-- Video quality setting entry. Videos will be recorded in VGA quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_vga" translatable="false">VGA</string>
    <!-- Video quality setting entry. Videos will be recorded in CIF quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_cif" translatable="false">CIF</string>
    <!-- Video quality setting entry. Videos will be recorded in QVGA quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_qvga" translatable="false">QVGA</string>
    <!-- Video quality setting entry. Videos will be recorded in QCIF quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_qcif" translatable="false">QCIF</string>

    <!-- Settings screen, Power Mode title -->
    <string name="pref_camera_powermode_title">Power Mode</string>

    <!-- Settings screen, Power mode dialog radio button choices -->
    <string name="pref_camera_powermode_entry_lp">Low_Power</string>
    <string name="pref_camera_powermode_entry_np">Normal_Power</string>

    <!-- Settings screen, Power mode dialog radio button choices -->
    <string name="pref_camera_powermode_value_lp">Low_Power</string>
    <string name="pref_camera_powermode_value_np">Normal_Power</string>

    <!-- Default Power mode.. -->
    <string name="pref_camera_powermode_default" translatable="false">Normal_Power</string>

    <!-- Default auto exposure setting. Do not translate. -->
    <string name="pref_camera_autoexposure_default">frame-average</string>
    <!-- Settings screen, Select auto exposure title -->
    <string name="pref_camera_autoexposure_title">Select Auto Exposure Mode</string>

    <!-- Settings screen, auto exposure dialog radio button choices -->
    <string name="pref_camera_autoexposure_entry_frameaverage">Frame Average</string>
    <string name="pref_camera_autoexposure_entry_centerweighted">Center Weighted</string>
    <string name="pref_camera_autoexposure_entry_spotmetering">Spot Metering</string>

    <!-- Auto exposure entry values. Do not translate. -->
    <string name="pref_camera_autoexposure_value_frameaverage">frame-average</string>
    <string name="pref_camera_autoexposure_value_centerweighted">center-weighted</string>
    <string name="pref_camera_autoexposure_value_spotmetering">spot-metering</string>

    <!-- Settings screen, Video encoder title -->
    <string name="pref_camera_videoencoder_title">Video Encoder</string>

    <!-- Settings screen, Video encoder dialog radio button choices -->
    <string name="pref_camera_videoencoder_entry_0">MPEG4</string>
    <string name="pref_camera_videoencoder_entry_1">H263</string>
    <string name="pref_camera_videoencoder_entry_2">H264</string>
    <string name="pref_camera_videoencoder_entry_3">H265</string>

    <!-- Default video encoder setting. A numerical value. -->
    <string name="pref_camera_videoencoder_default" translatable="false">h264</string>

    <!-- Settings screen, Video encoder dialog title -->
    <string name="pref_camera_videoencoder_dialogtitle">Video Encoder</string>

    <!-- Default audio encoder setting. A numerical value. -->
    <string name="pref_camera_audioencoder_default" translatable="false">aac</string>

    <!-- Settings screen, Audio encoder title -->
    <string name="pref_camera_audioencoder_title">Audio Encoder</string>

    <!-- Settings screen, Audio encoder dialog radio button choices -->
    <string name="pref_camera_audioencoder_entry_0">AMRNB</string>
    <string name="pref_camera_audioencoder_entry_1">AAC</string>
    <string name="pref_camera_audioencoder_entry_2">OFF</string>
    <!-- Default video duration setting. A numerical value.-->
    <string name="pref_camera_video_duration_default" translatable="false">10</string>

    <!-- Settings screen, Video duration title -->
    <string name="pref_camera_video_duration_title">Video duration</string>

    <!-- Settings screen, Video duration dialog radio button choices -->
    <string name="pref_camera_video_duration_entry_mms">30 seconds(MMS)</string>
    <string name="pref_camera_video_duration_entry_10">10 minutes</string>
    <string name="pref_camera_video_duration_entry_30">30 minutes</string>
    <string name="pref_camera_video_duration_entry_48">48 minutes</string>
    <string name="pref_camera_video_duration_entry_144">144 minutes</string>
    <string name="pref_camera_video_duration_entry_nolimit">no limit</string>

    <!-- Default Skin Tone Enhancement setting. Do not translate. -->
    <string name="pref_camera_skinToneEnhancement_default">disable</string>

    <!-- Settings screen, Select Skin Tone Enhancement title -->
    <string name="pref_camera_skinToneEnhancement_title">Skin Tone Enhancement</string>

    <!-- Settings screen, Skin Tone Enhancement radio button choices -->
    <string name="pref_camera_skinToneEnhancement_entry_enable">Enable</string>
    <string name="pref_camera_skinToneEnhancement_entry_disable">Disable</string>

    <!-- Skin Tone Enhancement entry values. Do not translate. -->
    <string name="pref_camera_skinToneEnhancement_value_enable">enable</string>
    <string name="pref_camera_skinToneEnhancement_value_disable">disable</string>
    <!-- Settings screen, setting title text for ZSL-->
    <string name="pref_camera_zsl_title">ZSL</string>

    <string name="pref_camera_zsl_default" translatable="false">on</string>

         <!-- Default selectable zone af setting. Do not translate. -->
     <string name="pref_camera_selectablezoneaf_default">auto</string>

     <!-- Settings screen, Selectable zone af title -->
     <string name="pref_camera_selectablezoneaf_title"> Selectable Zone Auto Focus</string>

     <!-- Settings screen, Selectable Zone Af dialog radio button choices -->
     <string name="pref_camera_selectablezoneaf_entry_auto">Auto</string>
     <string name="pref_camera_selectablezoneaf_entry_spotmetering">Spot Metering</string>
     <string name="pref_camera_selectablezoneaf_entry_centerweighted">Center Weighted</string>
     <string name="pref_camera_selectablezoneaf_entry_frameaverage">Frame Average</string>

     <!-- Auto exposure entry values. Do not translate. -->
     <string name="pref_camera_selectablezoneaf_value_auto">auto</string>
     <string name="pref_camera_selectablezoneaf_value_spotmetering">spot-metering</string>
     <string name="pref_camera_selectablezoneaf_value_centerweighted">center-weighted</string>
     <string name="pref_camera_selectablezoneaf_value_frameaverage">frame-average</string>

     <string name="pref_camera_advanced_feature_default">none</string>

     <!-- Settings screen, Selectable advanced features title -->
     <string name="pref_camera_advanced_features_title">Advanced Features</string>

     <!-- Settings screen, Selectable Zone Af dialog radio button choices -->
     <string name="pref_camera_advanced_feature_entry_none">None</string>
     <string name="pref_camera_advanced_feature_entry_ubifocus">Ubi Focus</string>
     <string name="pref_camera_advanced_feature_entry_refocus">Refocus</string>
     <string name="pref_camera_advanced_feature_entry_chromaflash">Chroma Flash</string>
     <string name="pref_camera_advanced_feature_entry_optizoom">Opti Zoom</string>
     <string name="pref_camera_advanced_feature_entry_FSSR">FSSR</string>
     <string name="pref_camera_advanced_feature_entry_trueportrait">True Portrait</string>
     <string name="pref_camera_advanced_feature_entry_multi_touch_focus">Multi-touch Focus</string>
     <string name="pref_camera_advanced_feature_entry_stillmore">Still More</string>

     <!-- Auto exposure entry values. Do not translate. -->
     <string name="pref_camera_advanced_feature_value_none">none</string>
     <string name="pref_camera_advanced_feature_value_ubifocus_on">af-bracket-on</string>
     <string name="pref_camera_advanced_feature_value_refocus_on">re-focus-on</string>
     <string name="pref_camera_advanced_feature_value_chromaflash_on">chroma-flash-on</string>
     <string name="pref_camera_advanced_feature_value_optizoom_on">opti-zoom-on</string>
     <string name="pref_camera_advanced_feature_value_FSSR_on">FSSR-on</string>
     <string name="pref_camera_advanced_feature_value_trueportrait_on">true-portrait-on</string>
     <string name="pref_camera_advanced_feature_value_multi_touch_focus_on">multi-touch-focus-on</string>
     <string name="pref_camera_advanced_feature_value_stillmore_on">still-more-on</string>

     <string name="pref_camera_advanced_feature_value_ubifocus_off">af-bracket-off</string>
     <string name="pref_camera_advanced_feature_value_refocus_off">re-focus-off</string>
     <string name="pref_camera_advanced_feature_value_chromaflash_off">chroma-flash-off</string>
     <string name="pref_camera_advanced_feature_value_optizoom_off">opti-zoom-off</string>
     <string name="pref_camera_advanced_feature_value_FSSR_off">FSSR-off</string>
     <string name="pref_camera_advanced_feature_value_trueportrait_off">true-portrait-off</string>
     <string name="pref_camera_advanced_feature_value_multi_touch_focus_off">multi-touch-focus-off</string>
     <string name="pref_camera_advanced_feature_value_stillmore_off">still-more-off</string>

     <!-- Manual exposure control entry values -->
     <string name="pref_camera_manual_exp_title">Manual Exposure</string>
     <string name="pref_camera_manual_exp_entry_ISO_priority">ISO priority</string>
     <string name="pref_camera_manual_exp_entry_exptime_priority">Exposure time priority</string>
     <string name="pref_camera_manual_exp_entry_user_setting">User setting</string>
    <string name="pref_camera_manual_exp_entry_gains_priority">Gains priority</string>
     <string name="pref_camera_manual_exp_entry_off">Off</string>

     <string name="pref_camera_manual_exp_default">off</string>
     <string name="pref_camera_manual_exp_value_ISO_priority">iso-priority</string>
     <string name="pref_camera_manual_exp_value_exptime_priority">exp-time-priority</string>
     <string name="pref_camera_manual_exp_value_user_setting">user-setting</string>
    <string name="pref_camera_manual_exp_value_gains_priority">gains-priority</string>
     <string name="pref_camera_manual_exp_value_off">off</string>

     <!-- Manual white balance entry values -->
     <string name="pref_camera_manual_wb_title">Manual White balance</string>
     <string name="pref_camera_manual_wb_entry_color_temperature">Color temperature</string>
     <string name="pref_camera_manual_wb_entry_rbgb_gains">RBGB gains</string>
     <string name="pref_camera_manual_wb_entry_off">Off</string>

     <string name="pref_camera_manual_wb_default">off</string>
     <string name="pref_camera_manual_wb_value_color_temperature">color-temperature</string>
     <string name="pref_camera_manual_wb_value_rbgb_gains">rbgb-gains</string>
     <string name="pref_camera_manual_wb_value_off">off</string>

     <!-- Manual Focus entry values -->
     <string name="pref_camera_manual_focus_title">Manual Focus</string>
     <string name="pref_camera_manual_focus_entry_scale_mode">Scale mode</string>
     <string name="pref_camera_manual_focus_entry_diopter_mode">Diopter mode</string>
     <string name="pref_camera_manual_focus_entry_off">Off</string>

     <string name="pref_camera_manual_focus_default">off</string>
     <string name="pref_camera_manual_focus_value_scale_mode">scale-mode</string>
     <string name="pref_camera_manual_focus_value_diopter_mode">diopter-mode</string>
     <string name="pref_camera_manual_focus_value_off">off</string>

    <!-- Instant Capture entry -->
    <string name="pref_camera_instant_capture_title" translatable="true">Instant Capture</string>
    <string name="pref_camera_instant_capture_entry_aggressive_aec" translatable="true">Aggressive AEC</string>
    <string name="pref_camera_instant_capture_entry_fast_aec" translatable="true">Fast AEC</string>
    <string name="pref_camera_instant_capture_entry_disable" translatable="true">Disable</string>

    <!-- Instant Capture entry values -->
    <string name="pref_camera_instant_capture_default" translatable="false">0</string>
    <string name="pref_camera_instant_capture_value_aggressive_aec" translatable="false">1</string>
    <string name="pref_camera_instant_capture_value_fast_aec" translatable="false">2</string>
    <string name="pref_camera_instant_capture_value_disable" translatable="false">0</string>

    <!-- Settings screen, ZSL location dialog choices -->
    <string name="pref_camera_zsl_entry_off">Off</string>
    <string name="pref_camera_zsl_entry_on">On</string>

    <string name="pref_camera_zsl_value_off">off</string>
    <string name="pref_camera_zsl_value_on">on</string>

    <!-- The message is shown in toast when HDR or scene mode is enabled -->
    <string name="hdr_enable_message">HDR is enabled, only Auto scene mode is supported</string>
    <string name="scene_enable_message">HDR is not supported in the scene mode other than Auto</string>

    <!-- Settings screen, Video HDR location dialog choices -->
    <string name="pref_camera_video_hdr_entry_off">Off</string>
    <string name="pref_camera_video_hdr_entry_on">On</string>

    <string name="pref_camera_video_hdr_value_off">off</string>
    <string name="pref_camera_video_hdr_value_on">on</string>

    <!-- Settings screen, CDS location dialog choices -->
    <string name="pref_camera_cds_entry_off">Off</string>
    <string name="pref_camera_cds_entry_on">On</string>
    <string name="pref_camera_cds_entry_auto">Auto</string>

    <string name="pref_camera_cds_value_off">off</string>
    <string name="pref_camera_cds_value_on">on</string>
    <string name="pref_camera_cds_value_auto">auto</string>

     <!-- Settings screen, video CDS location dialog choices -->
    <string name="pref_camera_video_cds_entry_off">Off</string>
    <string name="pref_camera_video_cds_entry_on">On</string>
    <string name="pref_camera_video_cds_entry_auto">Auto</string>

    <string name="pref_camera_video_cds_value_off">off</string>
    <string name="pref_camera_video_cds_value_on">on</string>
    <string name="pref_camera_video_cds_value_auto">auto</string>

    <!-- Settings screen, TNR location dialog choices -->
    <string name="pref_camera_tnr_entry_disable">Disable</string>
    <string name="pref_camera_tnr_entry_enable">Enable</string>

    <!-- Settings screen, TNR location dialog choices -->
    <string name="pref_camera_tnr_value_off">off</string>
    <string name="pref_camera_tnr_value_on">on</string>

    <!-- Settings screen, video TNR location dialog choices -->
    <string name="pref_camera_video_tnr_entry_disable">Disable</string>
    <string name="pref_camera_video_tnr_entry_enable">Enable</string>

    <!-- Settings screen, video TNR location dialog choices -->
    <string name="pref_camera_video_tnr_value_off">off</string>
    <string name="pref_camera_video_tnr_value_on">on</string>

    <!-- Settings screen, setting title text for Video HDR-->
    <string name="pref_camera_video_hdr_title">Video HDR</string>

    <string name="pref_camera_video_hdr_default" translatable="false">off</string>

    <!-- Settings menu, setting title text for cds mode-->
    <string name="pref_camera_cds_title">CDS mode</string>

    <!-- Settings menu, setting title text for video cds mode-->
    <string name="pref_camera_video_cds_title">Video CDS mode</string>

    <!-- Default cds mode setting-->
    <string name="pref_camera_cds_default" translatable="false">on</string>

    <!-- Default video cds mode setting-->
    <string name="pref_camera_video_cds_default" translatable="false">off</string>

     <!-- Settings menu, setting title text for tnr mode-->
    <string name="pref_camera_tnr_title">TNR mode</string>

    <!-- Settings menu, setting title text for video tnr mode-->
    <string name="pref_camera_video_tnr_title">Video TNR mode</string>

    <!-- Default tnr mode setting-->
    <string name="pref_camera_tnr_default" translatable="false">off</string>

    <!-- Default video tnr mode setting-->
    <string name="pref_camera_video_tnr_default" translatable="false">on</string>

    <!-- Default face  detection setting. -->
    <string name="pref_camera_facedetection_default" translatable="false">off</string>

    <!-- Settings screen, Select Face Detection -->
    <string name="pref_camera_facedetection_title">Face Detection</string>

    <!-- Settings menu, Face Detection choices -->
    <string name="pref_camera_facedetection_entry_off">Off</string>
    <string name="pref_camera_facedetection_entry_on">On</string>

    <!-- Default Selfie Flash setting. -->
    <string name="pref_selfie_flash_default" translatable="false">off</string>

    <!-- Settings screen, Selfie Flash Detection -->
    <string name="pref_selfie_flash_title">Selfie Flash</string>

    <!-- Settings menu, Selfie Flash choices -->
    <string name="pref_selfie_flash_entry_off">Off</string>
    <string name="pref_selfie_flash_entry_on">On</string>

     <!-- Default redeye reduction setting. -->
     <string name="pref_camera_redeyereduction_default" translatable="false">disable</string>

     <!-- Settings screen, Select Redeye Reduction -->
     <string name="pref_camera_redeyereduction_title">Redeye Reduction</string>

     <!-- Settings menu, redeye reduction choices -->
     <string name="pref_camera_redeyereduction_entry_enable">Enable</string>
     <string name="pref_camera_redeyereduction_entry_disable">Disable</string>

     <string name="pref_camera_selfiemirror_default" translatable="false">disable</string>
     <string name="pref_camera_selfiemirror_title" translatable="true">Selfie Mirror</string>
     <string name="pref_camera_selfiemirror_entry_enable">Enable</string>
     <string name="pref_camera_selfiemirror_entry_disable">Disable</string>

     <string name="pref_camera_shuttersound_default" translatable="false">enable</string>
     <string name="pref_camera_shuttersound_title" translatable="true">Shutter Sound</string>
     <string name="pref_camera_shuttersound_entry_enable">Enable</string>
     <string name="pref_camera_shuttersound_entry_disable">Disable</string>

     <!-- Default mce setting. Do not translate. -->
     <string name="pref_camera_mce_default">enable</string>

     <!-- The message is shown in toast when the app encounters an unsupported selection for HFR/HSR mode -->
     <string name="error_app_unsupported_hfr_selection">HFR/HSR is not supported for Image Stabilization/TimeLapse/HDR</string>

     <!-- The message is shown in dialog when the app encounters an unsupported resolution for HFR mode -->
     <string name="error_app_unsupported_hfr">HFR is not supported for selected resolution</string>

     <!-- The message is shown in toast when the app encounters an unsupported resolution for DIS mode -->
     <string name="error_app_unsupported_dis">Unsupported video resolution for Image Stabilization</string>

     <!-- The message is shown in dialog when the app
         encounters an unsupported resolution for HSR mode -->
     <string name="error_app_unsupported_hsr">
         HSR is not supported for selected resolution
     </string>

     <!-- The message is shown in dialog when the app encounters an unsupported video codec for HFR mode -->
     <string name="error_app_unsupported_hfr_codec">Only H264 video encoding is supported when HFR is ON</string>

     <!-- The message is shown in dialog when the app encounters an unsupported video resolution-->
     <string name="error_app_unsupported">Unsupported video resolution for this encoder type</string>

     <!-- The message is shown in dialog when the app encounters an unsupported video profile-->
     <string name="error_app_unsupported_profile">Unsupported video profile</string>

    <!--The message is shown in dialog when the raw snapshot is selected in zsl mode-->
    <string name="error_app_unsupported_raw">Raw picture format is not supported
        in zsl mode, change to JPEG
    </string>

     <!-- Settings screen, AE bracketing and HDR location dialog choices -->
     <string name="pref_camera_ae_bracket_hdr_entry_ae_bracket">AE-Bracket</string>

     <string name="pref_camera_ae_bracket_hdr_value_ae_bracket">AE-Bracket</string>

     <string name="pref_camera_ae_bracket_hdr_title">AE-Bracket</string>

     <string name="pref_camera_ae_bracket_hdr_default" translatable="false">Off</string>

     <string name="pref_camera_ae_bracket_hdr_entry_on">On</string>

     <string name="pref_camera_ae_bracket_hdr_entry_off">Off</string>

     <string name="err_app_unsupport_ae_bracket_code">no ae bracket support by Apps now</string>

        <!-- The messsage shown if user selects a snapshot resolution lower than the current video size. -->
    <string name="snapshot_lower_than_video">Video snapshot size lower than video size. Capping to Video size. Please select a resolution higher or equal to the current video size. </string>

    <!-- The messsage shown if user selects a snapshot resolution in low power mode. -->
    <string name="snapshotsize_low_powermode">We do not support picture
        resolution in Low Power Mode. Video size snapshot will be taken. Please
        select High power mode for picture size.
    </string>

    <!-- The messsage shown if user tries to take snapshot during D1/QCIF resolution. -->
    <string name="snapshot_qcif_and_d1">Only Video size snapshot supported for this resolution. </string>

     <!-- Default hfr setting. Do not translate. -->
     <string name="pref_camera_hfr_default">off</string>

     <!-- Settings screen, Select hfr title -->
     <string name="pref_camera_hfr_title">Video High FrameRate</string>

     <!-- Settings screen, hfr radio button choices -->
     <string name="pref_camera_hfr_entry_off">30FPS</string>
     <string name="pref_camera_hfr_entry_2x">HFR 60</string>
     <string name="pref_camera_hfr_entry_3x">HFR 90</string>
     <string name="pref_camera_hfr_entry_4x">HFR 120</string>
     <string name="pref_camera_hfr_entry_8x">HFR 240</string>
     <string name="pref_camera_hfr_entry_16x">HFR 480</string>
     <string name="pref_camera_hfr_entry_32x">HFR 960</string>
     <string name="pref_camera_hfr_entry_hsr_60">HSR 60</string>
     <string name="pref_camera_hfr_entry_hsr_90">HSR 90</string>
     <string name="pref_camera_hfr_entry_hsr_120">HSR 120</string>
     <string name="pref_camera_hfr_entry_hsr_240">HSR 240</string>
     <string name="pref_camera_hfr_entry_hsr_480">HSR 480</string>
     <string name="pref_camera_hfr_entry_hsr_960">HSR 960</string>
     <string name="pref_camera_hfr_entry_2x_ssm_120">Super SloMo 120->240</string>
     <string name="pref_camera_hfr_entry_4x_ssm_120">Super SloMo 120->480</string>
     <string name="pref_camera_hfr_entry_2x_ssm_240">Super SloMo 240->480</string>
     <string name="pref_camera_hfr_entry_4x_ssm_240">Super SloMo 240->960</string>
     <string name="pref_camera_hfr_entry_2x_ssm_480">Super SloMo 480->960</string>
     <string name="pref_camera_hfr_entry_4x_ssm_480">Super SloMo 480->1920</string>
     <string name="pref_camera_hfr_entry_2x_ssm_960">Super SloMo 960->1920</string>
     <string name="pref_camera_hfr_entry_4x_ssm_960">Super SloMo 960->3840</string>

     <!-- HFR entry values. Do not translate. -->
     <string name="pref_camera_hfr_value_off">off</string>
     <string name="pref_camera_hfr_value_2x">hfr60</string>
     <string name="pref_camera_hfr_value_3x">hfr90</string>
     <string name="pref_camera_hfr_value_4x">hfr120</string>
     <string name="pref_camera_hfr_value_8x">hfr240</string>
     <string name="pref_camera_hfr_value_16x">hfr480</string>
     <string name="pref_camera_hfr_value_32x">hfr960</string>
     <string name="pref_camera_hfr_value_hsr_60">hsr60</string>
     <string name="pref_camera_hfr_value_hsr_90">hsr90</string>
     <string name="pref_camera_hfr_value_hsr_120">hsr120</string>
     <string name="pref_camera_hfr_value_hsr_240">hsr240</string>
     <string name="pref_camera_hfr_value_hsr_480">hsr480</string>
     <string name="pref_camera_hfr_value_hsr_960">hsr960</string>
     <string name="pref_camera_hfr_value_2x_ssm_120">2x_120</string>
     <string name="pref_camera_hfr_value_4x_ssm_120">4x_120</string>
     <string name="pref_camera_hfr_value_2x_ssm_240">2x_240</string>
     <string name="pref_camera_hfr_value_4x_ssm_240">4x_240</string>
     <string name="pref_camera_hfr_value_2x_ssm_480">2x_480</string>
     <string name="pref_camera_hfr_value_4x_ssm_480">4x_480</string>
     <string name="pref_camera_hfr_value_2x_ssm_960">2x_960</string>
     <string name="pref_camera_hfr_value_4x_ssm_960">4x_960</string>

     <!-- Default noise reduction setting. Do not translate. -->
     <string name="pref_camera_noise_reduction_default">off</string>

     <!-- Settings screen, Select See More title -->
     <string name="pref_camera_noise_reduction_title">Noise Reduction</string>

     <!-- Settings screen, see more radio button choices -->
     <string name="pref_camera_noise_reduction_entry_off">Off</string>
     <string name="pref_camera_noise_reduction_entry_fast">Fast</string>
     <string name="pref_camera_noise_reduction_entry_high_quality">High Quality</string>

     <!-- see more entry values. Do not translate. -->
     <string name="pref_camera_noise_reduction_value_off">off</string>
     <string name="pref_camera_noise_reduction_value_fast">fast</string>
     <string name="pref_camera_noise_reduction_value_high_quality">high-quality</string>

     <!-- Default see more setting. Do not translate. -->
     <string name="pref_camera_see_more_default">off</string>

     <!-- Settings screen, Select See More title -->
     <string name="pref_camera_see_more_title">See More</string>

     <!-- Settings screen, see more radio button choices -->
     <string name="pref_camera_see_more_entry_off">Off</string>
     <string name="pref_camera_see_more_entry_on">On</string>

     <!-- see more entry values. Do not translate. -->
     <string name="pref_camera_see_more_value_off">off</string>
     <string name="pref_camera_see_more_value_on">on</string>

        <!-- Denoise Settings screen, setting title text -->
    <string name="pref_camera_denoise_title">Wavelet Denoise Mode</string>
    <string name="pref_camera_denoise_default" translatable="false">denoise-on</string>

    <!-- Denoise Settings screen, Record location dialog choices -->
    <string name="pref_camera_denoise_entry_off">Off</string>
    <string name="pref_camera_denoise_entry_on">On</string>

     <!-- Default iso setting. Do not translate. -->
     <string name="pref_camera_iso_default">auto</string>

     <!-- Settings screen, Select Iso title -->
     <string name="pref_camera_iso_title">ISO</string>

     <!-- Settings screen, ISO dialog radio button choices -->
     <string name="pref_camera_iso_entry_auto">Auto</string>
     <string name="pref_camera_iso_entry_isodeblur">ISO Auto(HJR)</string>
     <string name="pref_camera_iso_entry_iso100">ISO100</string>
     <string name="pref_camera_iso_entry_iso200">ISO200</string>
     <string name="pref_camera_iso_entry_iso400">ISO400</string>
     <string name="pref_camera_iso_entry_iso800">ISO800</string>
     <string name="pref_camera_iso_entry_iso1600">ISO1600</string>
     <string name="pref_camera_iso_entry_iso3200">ISO3200</string>

     <!-- ISO entry values. Do not translate. -->
     <string name="pref_camera_iso_value_auto">auto</string>
     <string name="pref_camera_iso_value_isodeblur">ISO_HJR</string>
     <string name="pref_camera_iso_value_iso100">ISO100</string>
     <string name="pref_camera_iso_value_iso200">ISO200</string>
     <string name="pref_camera_iso_value_iso400">ISO400</string>
     <string name="pref_camera_iso_value_iso800">ISO800</string>
     <string name="pref_camera_iso_value_iso1600">ISO1600</string>
     <string name="pref_camera_iso_value_iso3200">ISO3200</string>

     <!-- Settings screen, Anti Banding title -->
     <string name="pref_camera_antibanding_title">Anti Banding</string>

     <!-- Default Anti Banding setting. -->
     <string name="pref_camera_antibanding_default">off</string>

     <!-- Settings screen, Anti Banding entries -->
     <string name="pref_camera_antibanding_entry_0">Off</string>
     <string name="pref_camera_antibanding_entry_1">50 Hz</string>
     <string name="pref_camera_antibanding_entry_2">60 Hz</string>
     <string name="pref_camera_antibanding_entry_3">Auto</string>

        <!-- Default color effect setting. -->
    <string name="pref_camera_coloreffect_default" translatable="false">none</string>
    <string name="pref_camera_filter_mode_entry_on">On</string>
    <string name="pref_camera_filter_mode_entry_off">Off</string>

    <!-- Settings screen, Select Color effect title -->
    <string name="pref_camera_coloreffect_title">Color effect Level</string>
    <string name="pref_camera_filter_mode_title">Color effect Mode</string>

    <!-- Settings screen, Color effect dialog radio button choices -->
    <string name="pref_camera_coloreffect_entry_none">None</string>
    <string name="pref_camera_coloreffect_entry_mono">Mono</string>
    <string name="pref_camera_coloreffect_entry_sepia">Sepia</string>
    <string name="pref_camera_coloreffect_entry_negative">Negative</string>
    <!-- Color effect that overexposes and reverses some of the tones. -->
    <string name="pref_camera_coloreffect_entry_solarize">Solarize</string>
    <string name="pref_camera_coloreffect_entry_posterize">Posterize</string>
    <string name="pref_camera_coloreffect_entry_aqua">Aqua</string>
    <string name="pref_camera_coloreffect_entry_emboss">Emboss</string>
    <string name="pref_camera_coloreffect_entry_sketch">Sketch</string>
    <string name="pref_camera_coloreffect_entry_neon">Neon</string>
    <string name="pref_camera_coloreffect_entry_pastel">Pastel</string>
    <string name="pref_camera_coloreffect_entry_mosaic">Mosaic</string>
    <string name="pref_camera_coloreffect_entry_redtint">Red tint</string>
    <string name="pref_camera_coloreffect_entry_bluetint">Blue tint</string>
    <string name="pref_camera_coloreffect_entry_greentint">Green tint</string>

    <!-- Default Touch AF/AEC setting. Do not translate. -->
    <string name="pref_camera_touchafaec_default">touch-on</string>

    <!-- Settings screen, Select Touch AF/AEC title -->
    <string name="pref_camera_touchafaec_title">Touch AF/AEC</string>

    <!-- Settings screen, Touch AF/AEC dialog radio button choices -->
    <string name="pref_camera_touchafaec_entry_off">Off</string>
    <string name="pref_camera_touchafaec_entry_on">On</string>

    <!-- Select IS title -->
    <string name="pref_camera_dis_title">Image Stabilization</string>

    <!-- DIS choices -->
    <string name="pref_camera_dis_entry_off">Off</string>
    <string name="pref_camera_dis_entry_on">On</string>

    <string name="pref_camera_dis_default">disable</string>
    <string name="pref_camera_dis_value_disable">disable</string>
    <string name="pref_camera_dis_value_enable">enable</string>

    <!-- Select Face Recognition title -->
    <string name="pref_camera_facerc_title">Face Recognition</string>

    <string name="pref_camera_facerc_default">off</string>

    <!-- Face Recognition choices -->
    <string name="pref_camera_facerc_entry_off">Off</string>
    <string name="pref_camera_facerc_entry_on">On</string>

    <!-- Settings screen, Multiple levels entries -->
    <string name="pref_camera_sharpness_entry_level0">Level 0</string>
    <string name="pref_camera_sharpness_entry_level1">Level 1</string>
    <string name="pref_camera_sharpness_entry_level2">Level 2(Default)</string>
    <string name="pref_camera_sharpness_entry_level3">Level 3</string>
    <string name="pref_camera_sharpness_entry_level4">Level 4</string>
    <string name="pref_camera_sharpness_entry_level5">Level 5</string>
    <string name="pref_camera_sharpness_entry_level6">Level 6</string>

    <!-- Settings screen, Multiple levels entries -->
    <string name="pref_camera_contrast_entry_level0">Level 0</string>
    <string name="pref_camera_contrast_entry_level1">Level 1</string>
    <string name="pref_camera_contrast_entry_level2">Level 2</string>
    <string name="pref_camera_contrast_entry_level3">Level 3</string>
    <string name="pref_camera_contrast_entry_level4">Level 4</string>
    <string name="pref_camera_contrast_entry_level5">Level 5(Default)</string>
    <string name="pref_camera_contrast_entry_level6">Level 6</string>
    <string name="pref_camera_contrast_entry_level7">Level 7</string>
    <string name="pref_camera_contrast_entry_level8">Level 8</string>
    <string name="pref_camera_contrast_entry_level9">Level 9</string>
    <string name="pref_camera_contrast_entry_level10">Level 10</string>

    <!-- Settings screen, Multiple levels entries -->
    <string name="pref_camera_saturation_entry_level0">Level 0</string>
    <string name="pref_camera_saturation_entry_level1">Level 1</string>
    <string name="pref_camera_saturation_entry_level2">Level 2</string>
    <string name="pref_camera_saturation_entry_level3">Level 3</string>
    <string name="pref_camera_saturation_entry_level4">Level 4</string>
    <string name="pref_camera_saturation_entry_level5">Level 5(Default)</string>
    <string name="pref_camera_saturation_entry_level6">Level 6</string>
    <string name="pref_camera_saturation_entry_level7">Level 7</string>
    <string name="pref_camera_saturation_entry_level8">Level 8</string>
    <string name="pref_camera_saturation_entry_level9">Level 9</string>
    <string name="pref_camera_saturation_entry_level10">Level 10</string>

    <!-- Default sharpness setting. Do not translate. -->
    <string name="pref_camera_sharpness_default">2</string>

    <!-- Settings screen, Select sharpness title -->
    <string name="pref_camera_sharpness_title">Sharpness</string>

    <!-- Default contrast setting. Do not translate. -->
    <string name="pref_camera_contrast_default">5</string>

    <!-- Settings screen, Select contrast title -->
    <string name="pref_camera_contrast_title">Contrast</string>

    <!-- Default saturation setting. Do not translate. -->
    <string name="pref_camera_saturation_default">5</string>

    <!-- Settings screen, Select saturation title -->
    <string name="pref_camera_saturation_title">Saturation</string>

    <!-- Default picture quality setting. See
         pref_camera_jpegquality_entryvalues for possible values -->
    <string name="pref_camera_jpegquality_default" translatable="false">100</string>

    <!-- Default Picture format setting. Do not translate. -->
    <string name="pref_camera_picture_format_default">jpeg</string>

    <!-- Settings screen, Select Picture format title -->
    <string name="pref_camera_picture_format_title">Select Picture Format</string>

    <!-- Settings screen, picture format dialog radio button choices -->
    <string name="pref_camera_picture_format_entry_jpeg">Jpeg</string>
    <string name="pref_camera_picture_format_entry_raw">Raw</string>
    <string name="pref_camera_picture_format_entry_raw_yuv_422_sp">YUV422SP</string>
    <string name="pref_camera_picture_format_entry_raw_yuv_8bit_yuyv">YUVRaw8YUYV</string>
    <string name="pref_camera_picture_format_entry_raw_yuv_8bit_yvyu">YUVRaw8YVYU</string>
    <string name="pref_camera_picture_format_entry_raw_yuv_8bit_uyvy">YUVRaw8UYVY</string>
    <string name="pref_camera_picture_format_entry_raw_yuv_8bit_vyuy">YUVRaw8VYUY</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_8gbrg">BayerQ8GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_8grbg">BayerQ8GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_8rggb">BayerQ8RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_8bggr">BayerQ8BGRR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_10gbrg">BayerQ10GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_10grbg">BayerQ10GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_10rggb">BayerQ10RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_10bggr">BayerQ10BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_12gbrg">BayerQ12GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_12grbg">BayerQ12GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_12rggb">BayerQ12RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_qcom_12bggr">BayerQ12BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_8gbrg">BayerM8GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_8grbg">BayerM8GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_8rggb">BayerM8RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_8bggr">BayerM8BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_10gbrg">BayerM10GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_10grbg">BayerM10GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_10rggb">BayerM10RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_10bggr">BayerM10BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_12gbrg">BayerM12GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_12grbg">BayerM12GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_12rggb">BayerM12RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_mipi_12bggr">BayerM12BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8gbrg">BayerIQ8GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8grbg">BayerIQ8GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8rggb">BayerIQ8RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_8bggr">BayerIQ8BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10gbrg">BayerIQ10GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10grbg">BayerIQ10GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10rggb">BayerIQ10RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_10bggr">BayerIQ10BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12gbrg">BayerIQ12GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12grbg">BayerIQ12GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12rggb">BayerIQ12RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_qcom_12bggr">BayerIQ12BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8gbrg">BayerIM8GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8grbg">BayerIM8GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8rggb">BayerIM8RGGB</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_8bggr">BayerIM8BGGR</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10gbrg">BayerIM10GBRG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10grbg">BayerIM10GRBG</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10rggb">BayerIM10rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_10bggr">BayerIM8bggr</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12gbrg">BayerIM12gbrg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12grbg">BayerIM12grbg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12rggb">BayerIM12rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_mipi_12bggr">BayerIM12bggr</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8gbrg">BayerIP88gbrg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8grbg">BayerIP88grbg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8rggb">BayerIP88rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain8_8bggr">BayerIP88bggr</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8gbrg">BayerIP168gbrg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8grbg">BayerIP168grbg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8rggb">BayerIP168rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_8bggr">BayerIP168bggr</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10gbrg">BayerIP1610gbrg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10grbg">BayerIP1610grbg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10rggb">BayerIP1610rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_10bggr">BayerIP1610bggr</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12gbrg">BayerIP1612gbrg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12grbg">BayerIP1612grbg</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12rggb">BayerIP1612rggb</string>
    <string name="pref_camera_picture_format_entry_raw_bayer_ideal_plain16_12bggr">BayerIP1612bggr</string>

    <!-- Picture format entry values. Do not translate. -->
    <string name="pref_camera_picture_format_value_jpeg">jpeg</string>
    <string name="pref_camera_picture_format_value_raw">raw</string>
    <string name="pref_camera_picture_format_value_raw_yuv_422_sp">yuv422sp</string>
    <string name="pref_camera_picture_format_value_raw_yuv_8bit_yuyv">yuv-raw8-yuyv</string>
    <string name="pref_camera_picture_format_value_raw_yuv_8bit_yvyu">yuv-raw8-yvyu</string>
    <string name="pref_camera_picture_format_value_raw_yuv_8bit_uyvy">yuv-raw8-uyvy</string>
    <string name="pref_camera_picture_format_value_raw_yuv_8bit_vyuy">yuv-raw8-vyuy</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_8gbrg">bayer-qcom-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_8grbg">bayer-qcom-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_8rggb">bayer-qcom-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_8bggr">bayer-qcom-8bgrr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_10gbrg">bayer-qcom-10gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_10grbg">bayer-qcom-10grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_10rggb">bayer-qcom-10rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_10bggr">bayer-qcom-10bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_12gbrg">bayer-qcom-12gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_12grbg">bayer-qcom-12grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_12rggb">bayer-qcom-12rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_qcom_12bggr">bayer-qcom-12bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_8gbrg">bayer-mipi-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_8grbg">bayer-mipi-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_8rggb">bayer-mipi-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_8bggr">bayer-mipi-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_10gbrg">bayer-mipi-10gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_10grbg">bayer-mipi-10grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_10rggb">bayer-mipi-10rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_10bggr">bayer-mipi-10bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_12gbrg">bayer-mipi-12gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_12grbg">bayer-mipi-12grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_12rggb">bayer-mipi-12rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_mipi_12bggr">bayer-mipi-12bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_8gbrg">bayer-ideal-qcom-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_8grbg">bayer-ideal-qcom-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_8rggb">bayer-ideal-qcom-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_8bggr">bayer-ideal-qcom-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_10gbrg">bayer-ideal-qcom-10gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_10grbg">bayer-ideal-qcom-10grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_10rggb">bayer-ideal-qcom-10rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_10bggr">bayer-ideal-qcom-10bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_12gbrg">bayer-ideal-qcom-12gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_12grbg">bayer-ideal-qcom-12grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_12rggb">bayer-ideal-qcom-12rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_qcom_12bggr">bayer-ideal-qcom-12bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_8gbrg">bayer-ideal-mipi-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_8grbg">bayer-ideal-mipi-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_8rggb">bayer-ideal-mipi-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_8bggr">bayer-ideal-mipi-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_10gbrg">bayer-ideal-mipi-10gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_10grbg">bayer-ideal-mipi-10grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_10rggb">bayer-ideal-mipi-10rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_10bggr">bayer-ideal-mipi-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_12gbrg">bayer-ideal-mipi-12gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_12grbg">bayer-ideal-mipi-12grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_12rggb">bayer-ideal-mipi-12rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_mipi_12bggr">bayer-ideal-mipi-12bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain8_8gbrg">bayer-ideal-plain8-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain8_8grbg">bayer-ideal-plain8-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain8_8rggb">bayer-ideal-plain8-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain8_8bggr">bayer-ideal-plain8-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_8gbrg">bayer-ideal-plain16-8gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_8grbg">bayer-ideal-plain16-8grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_8rggb">bayer-ideal-plain16-8rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_8bggr">bayer-ideal-plain16-8bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_10gbrg">bayer-ideal-plain16-10gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_10grbg">bayer-ideal-plain16-10grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_10rggb">bayer-ideal-plain16-10rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_10bggr">bayer-ideal-plain16-10bggr</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_12gbrg">bayer-ideal-plain16-12gbrg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_12grbg">bayer-ideal-plain16-12grbg</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_12rggb">bayer-ideal-plain16-12rggb</string>
    <string name="pref_camera_picture_format_value_raw_bayer_ideal_plain16_12bggr">bayer-ideal-plain16-12bggr</string>
    <!-- Settings screen, Picture quality title -->
    <string name="pref_camera_jpegquality_title">Picture quality</string>

    <!-- Settings screen, Picture quality dialog radio button choices -->
    <string name="pref_camera_jpegquality_entry_0">Low</string>
    <string name="pref_camera_jpegquality_entry_1">Standard</string>
    <string name="pref_camera_jpegquality_entry_2">High</string>

     <!-- Default auto histogram setting. Do not translate. -->
     <string name="pref_camera_histogram_default">disable</string>

     <!-- Settings screen, Select Histogram title -->
     <string name="pref_camera_histogram_title">Histogram</string>

     <!-- Settings screen, Histogram radio button choices -->
     <string name="pref_camera_histogram_entry_enable">Enable</string>
     <string name="pref_camera_histogram_entry_disable">Disable</string>

     <!-- Histogram entry values. Do not translate. -->
     <string name="pref_camera_histogram_value_enable">enable</string>
     <string name="pref_camera_histogram_value_disable">disable</string>

    <string name="pref_camera_picturesize_entry_1920x1080">HD1080</string>
    <string name="pref_camera_picturesize_entry_1280x768">WXGA</string>
    <string name="pref_camera_picturesize_entry_1280x720">HD720</string>
    <string name="pref_camera_picturesize_entry_1280x400">1280 x 400</string>
    <string name="pref_camera_picturesize_entry_800x600">SVGA</string>
    <string name="pref_camera_picturesize_entry_800x480">WVGA</string>
    <string name="pref_camera_picturesize_entry_960x720">960 x 720</string>
    <string name="pref_camera_picturesize_entry_720x480">720 x 480</string>
    <string name="pref_camera_picturesize_entry_352x288">CIF</string>
    <string name="pref_camera_picturesize_entry_176x144">QCIF</string>

    <string name="pref_camera_focusmode_entry_normal">Normal</string>
    <string name="pref_camera_focusmode_entry_continuous">CAF</string>

    <string name="pref_camera_scenemode_entry_asd">ASD</string>
    <!-- Scene mode that takes an image with as little noise as possible so details of the subject are maintained. -->
    <string name="pref_camera_scenemode_entry_portrait">Portrait</string>
    <!-- Scene mode that takes an image of a landscape assuming all subjects are relatively far from the phone. -->
    <string name="pref_camera_scenemode_entry_landscape">Landscape</string>

     <!-- Scene mode that takes night shots with long exposure and flash to illuminate the subject while still capturing darker detail in the background. -->
    <string name="pref_camera_scenemode_entry_night_portrait">Night Portrait</string>
    <!-- Scene mode to be used in a stage show. -->
    <string name="pref_camera_scenemode_entry_theatre">Theatre</string>
    <!-- Scene mode optimized for taking images on the beach. -->
    <string name="pref_camera_scenemode_entry_beach">Beach</string>
    <!-- Scene mode optimized for taking images in the snow. -->
    <string name="pref_camera_scenemode_entry_snow">Snow</string>

    <!-- Scene mode for taking a photo as sharp as possible. -->
    <string name="pref_camera_scenemode_entry_steadyphoto">Steady Photo</string>
    <!-- Scene mode optimized for taking images of fireworks. -->
    <string name="pref_camera_scenemode_entry_fireworks">Fireworks</string>
    <!-- Scene mode optimized for taking images during sports . -->
    <string name="pref_camera_scenemode_entry_sports">Sports</string>
    <!-- Scene mode optimized for capturing the naturally warm color of scenes lit by candles. [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_candlelight">Candlelight</string>
    <!-- Scene mode for taking a photo with backlight. -->
    <string name="pref_camera_scenemode_entry_backlight">Backlight</string>
    <!-- Scene mode optimized for taking images of flowers. -->
    <string name="pref_camera_scenemode_entry_flowers">Flowers</string>
    <!-- Scene mode optimized for taking images of AR. -->
    <string name="pref_camera_scenemode_entry_ar">AR</string>

    <!-- Default auto scene detection setting. -->
    <string name="pref_camera_scenedetect_default" translatable="false">off</string>

    <!-- Settings screen, Select Auto Scene Detection -->
    <string name="pref_camera_scenedetect_title">Auto Scene Detection</string>

    <!-- Settings menu, auto scene detection choices -->
    <string name="pref_camera_scenedetect_entry_off">Off</string>
    <!-- Scene detection set to on - supports backlight and snow cloudy detection. -->
    <string name="pref_camera_scenedetect_entry_on">On</string>

    <!-- Toast showing error if invalid size selected for  time lapse  [CHAR LIMIT=65] -->
    <string name="time_lapse_error">Invalid video-size for time-lapse recording.</string>

    <string name="pref_camera_videosnapsize_default" translatable="false">auto</string>

    <!-- longshot value -->
    <string name="pref_camera_longshot_default" translatable="false">off</string>
    <string name="pref_camera_longshot_title">Continuous Shot</string>
    <string name="msg_cancel_longshot_for_limited_memory">run out of memory, canceling longshot</string>
    <string name="msg_not_allow_settings_in_longshot">Cannot launch settings during processing longshot</string>

    <string name="pref_camera_gridline_title">Grid Line</string>
    <!-- Toast showing non-supported functionality for flash in AE bracket -->
    <string name="flash_aebracket_message">Flash is not supported in AE-Bracket Mode</string>

    <!-- Settings screen, setting title text for Auto HDR-->
    <string name="pref_camera_auto_hdr_title">AUTO HDR</string>
    <string name="pref_camera_auto_hdr_default" translatable="false">
    disable</string>

    <!-- Settings screen, Auto HDR location dialog choices -->
    <string name="pref_camera_auto_hdr_entry_enable">Enable</string>
    <string name="pref_camera_auto_hdr_entry_disable">Disable</string>
    <string name="pref_camera_auto_hdr_value_enable">enable</string>
    <string name="pref_camera_auto_hdr_value_disable">disable</string>

    <!-- toast message for storage location switch -->
    <string name="on_switch_save_path_to_sdcard">Internal storage space is not enough - switched save path to SD card</string>

    <string name="pref_camera_hdr_mode_default">hdr-mode-multiframe</string>

    <!-- Settings screen, Selectable HDR mode title -->
    <string name="pref_camera_hdr_mode_title">HDR Mode</string>

    <!-- Settings screen, Selectable HDR mode radio button choices -->
    <string name="pref_camera_hdr_mode_entry_sensor">Sensor HDR</string>
    <string name="pref_camera_hdr_mode_entry_multi_frame">Multi-frame HDR</string>

    <!-- HDR mode entry values. Do not translate. -->
    <string name="pref_hdr_mode_value_sensor">hdr-mode-sensor</string>
    <string name="pref_hdr_mode_value_multi_frame">hdr-mode-multiframe</string>

    <!-- Settings screen, Selectable HDR need 1x frame default value -->
    <string name="pref_camera_hdr_need_1x_default">@string/pref_hdr_need_1x_value_true</string>

    <!-- Settings screen, Selectable HDR need 1x frame title -->
    <string name="pref_camera_hdr_need_1x_title">HDR need 1x frame</string>

    <!-- Settings screen, Selectable HDR need 1x frame radio button choices -->
    <string name="pref_camera_hdr_need_1x_entry_false">@string/setting_off</string>
    <string name="pref_camera_hdr_need_1x_entry_true">@string/setting_on</string>

    <!-- HDR need 1x frame entry values. Do not translate. -->
    <string name="pref_hdr_need_1x_value_false">false</string>
    <string name="pref_hdr_need_1x_value_true">true</string>

    <!--Default video rotation setting-->
    <string name="pref_camera_video_rotation_default" translatable="false">0</string>

    <!--Settings screen, video rotation title-->
    <string name="pref_camera_video_rotation_title">Video Rotation</string>

    <!--Menu, video rotation label-->
    <string name="pref_camera_video_rotation_label">VIDEO ROTATION</string>

    <!-- Settings screen, Video rotation dialog radio button choices -->
    <string name="pref_camera_video_rotation_entry_0">0</string>
    <string name="pref_camera_video_rotation_entry_90">90</string>
    <string name="pref_camera_video_rotation_entry_180">180</string>
    <string name="pref_camera_video_rotation_entry_270">270</string>

    <!-- The message is shown in toast when click showOnMap Menu and there is no map app -->
    <string name="map_activity_not_found_err">There is no map app for show location.</string>

    <!-- Continous shot enable message -->
    <string name="longshot_enable_message">
            Advance features are not supported in continuous shot mode</string>
    <!-- Advance features enable message -->
    <string name="advance_feature_enable_msg">
            Continuous shot is not supported when advance features are enabled</string>

    <!-- The alas of CameraActivity for gesture operation -->
    <string name="camera_gesture_title">Launch camera</string>
     <string name="pref_camera_tsmakeup_title">Beautification</string>
     <string name="pref_camera_tsmakeup_entry_on">On</string>
     <string name="pref_camera_tsmakeup_entry_off">Off</string>
     <string name="pref_camera_tsmakeup_entry_low">Low</string>
     <string name="pref_camera_tsmakeup_entry_mid">Mid</string>
     <string name="pref_camera_tsmakeup_entry_high">High</string>
     <string name="pref_camera_tsmakeup_default" translatable="false">Off</string>
     <string name="pref_camera_tsmakeup_level_default" translatable="false">60</string>
     <string name="pref_camera_tsmakeup_custom">Custom</string>
     <string name="pref_camera_tsmakeup_level_whiten">Whiten</string>
     <string name="pref_camera_tsmakeup_level_clean">Soften</string>
     <string name="text_tsmakeup_alert_title">Warning</string>
     <string name="text_tsmakeup_alert_msg">The beautification must turn on Face Detection, Continue or Quit?</string>
     <string name="text_tsmakeup_alert_continue">Continue</string>
     <string name="text_tsmakeup_alert_quit">Quit</string>
    <string name="text_tsmakeup_beautify_toast" translatable="true">Beautify</string>

    <string name="all_in_focus">All in Focus</string>
    <string name="refocus_toast">To re-focus last photo, tap here</string>
    <string name="refocus_prompt_title">UbiFocus</string>
    <string name="refocus_prompt_message">With UbiFocus, your camera will take 5 pictures, each time that you press the shutter button. The 5 pictures will be combined into 1 picture, where everything is in focus. You can choose to re-focus, after you take the picture.</string>

    <string name="camera_thermal_shutdown">Phone overheated, closing camera.</string>
    <string name="camera_server_died">Media server died, closing camera.</string>
    <string name="camera_unknown_error">Unknown error, closing camera.</string>

    <string name="mute_button_desc">Mute Button</string>

    <!-- Help menu strings -->
    <string name="help_menu_ok" translatable="true">OK</string>
    <string name="help_menu_scene_mode_1" translatable="true">Select a</string>
    <string name="help_menu_scene_mode_2" translatable="true">Scene Mode</string>
    <string name="help_menu_scene_mode_3" translatable="true">for better pictures</string>
    <string name="help_menu_color_filter_1" translatable="true">Apply color</string>
    <string name="help_menu_color_filter_2" translatable="true">filters</string>
    <string name="help_menu_color_filter_3" translatable="true">to</string>
    <string name="help_menu_color_filter_4" translatable="true">improve pictures</string>
    <string name="help_menu_beautify_1" translatable="true">Enhance portraits</string>
    <string name="help_menu_beautify_2" translatable="true">with the</string>
    <string name="help_menu_beautify_3" translatable="true">Beautify feature</string>
    <string name="help_menu_switcher_1" translatable="true">Switch</string>
    <string name="help_menu_switcher_2" translatable="true">between</string>
    <string name="help_menu_switcher_3" translatable="true">camera, video, and panorama</string>

    <string name="pref_camera2_camera2_title" translatable="true">Camera2 Mode</string>
    <string name="pref_camera2_camera2_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_camera2_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera2_camera2_default" translatable="false">disable</string>
    <string name="pref_camera2_camera2_value_enable" translatable="false">enable</string>
    <string name="pref_camera2_camera2_value_disable" translatable="false">disable</string>

    <string name="pref_camera2_mono_only_title" translatable="true">Mono Only</string>
    <string name="pref_camera2_mono_only_default" translatable="false">off</string>

    <string name="pref_camera2_mono_preview_title" translatable="true">Mono Preview</string>
    <string name="pref_camera2_mono_preview_entry_on" translatable="true">On</string>
    <string name="pref_camera2_mono_preview_entry_off" translatable="true">Off</string>
    <string name="pref_camera2_mono_preview_default" translatable="false">off</string>
    <string name="pref_camera2_mono_preview_value_on" translatable="false">on</string>
    <string name="pref_camera2_mono_preview_value_off" translatable="false">off</string>

    <string name="pref_camera2_clearsight_title" translatable="true">ClearSight</string>
    <string name="pref_camera2_clearsight_default" translatable="false">on</string>
    <string name="pref_camera2_clearsight_entry_on" translatable="true">On</string>
    <string name="pref_camera2_clearsight_entry_off" translatable="true">Off</string>

    <string name="pref_camera2_clearsight_value_on" translatable="false">on</string>
    <string name="pref_camera2_clearsight_value_off" translatable="false">off</string>

    <string name="clearsight_capture_success">ClearSight capture successful</string>
    <string name="clearsight_capture_fail">ClearSight capture failed</string>

    <string name="pref_camera2_mpo_title" translatable="true">MPO Format</string>
    <string name="pref_camera2_mpo_default" translatable="false">off</string>
    <string name="pref_camera2_mpo_entry_on" translatable="true">On</string>
    <string name="pref_camera2_mpo_entry_off" translatable="true">Off</string>

    <string name="pref_camera2_mpo_value_on" translatable="false">on</string>
    <string name="pref_camera2_mpo_value_off" translatable="false">off</string>

    <string name="pref_camera2_scenemode_default" translatable="false">0</string>
    <string name="pref_camera2_whitebalance_default" translatable="false">1</string>
    <string name="pref_camera2_coloreffect_default" translatable="false">0</string>
    <string name="pref_camera2_flashmode_value_auto" translatable="false">auto</string>

    <string name="pref_camera2_makeup_title" translatable="true">Makeup</string>

    <string name="pref_camera2_noise_reduction_default" translatable="false">high-quality</string>
    <string name="pref_camera2_noise_reduction_title" translatable="true">Noise Reduction</string>

    <string name="pref_camera2_noise_reduction_entry_off" translatable="true">@string/pref_camera_noise_reduction_entry_off</string>
    <string name="pref_camera2_noise_reduction_entry_fast" translatable="true">@string/pref_camera_noise_reduction_entry_fast</string>
    <string name="pref_camera2_noise_reduction_entry_high_quality" translatable="true">@string/pref_camera_noise_reduction_entry_high_quality</string>

    <string name="pref_camera2_noise_reduction_value_off" translatable="false">off</string>
    <string name="pref_camera2_noise_reduction_value_fast" translatable="false">fast</string>
    <string name="pref_camera2_noise_reduction_value_high_quality" translatable="false">high-quality</string>

    <string name="pref_camera2_videosnap_default" translatable="false">enable</string>
    <string name="pref_camera2_videosnap_title" translatable="true">Auto Snapshot Size</string>

    <string name="pref_camera2_videosnap_value_enable" translatable="true">enable</string>
    <string name="pref_camera2_videosnap_value_disable" translatable="true">disable</string>

    <string name="pref_camera2_videosnap_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_videosnap_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera2_trackingfocus_title" translatable="true">Tracking Focus</string>
    <string name="pref_camera_scenemode_entry_bestpicture" translatable="true">BestPicture</string>
    <string name="pref_camera_scenemode_entry_chromaflash" translatable="true">ChromaFlash</string>
    <string name="pref_camera_scenemode_entry_blurbuster" translatable="true">BlurBuster</string>
    <string name="pref_camera_scenemode_entry_sharpshooter" translatable="true">SharpPhoto</string>
    <string name="pref_camera_scenemode_entry_trackingfocus" translatable="true">TrackingFocus</string>
    <string name="pref_camera_scenemode_entry_panorama" translatable="true">Panorama</string>
    <string name="pref_camera_scenemode_entry_promode" translatable="true">ProMode</string>
    <string name="pref_camera_scenemode_entry_deepzoom" translatable="true">DeepZoom</string>
    <string name="bestpicture_done" translatable="true">SAVE</string>
    <string name="bestpicture_at_least_one_picture" translatable="true">At least, one picture has to be chosen.</string>

    <string name="pref_camera2_shutter_sound_default" translatable="true">on</string>
    <string name="pref_camera2_shutter_sound_value_on" translatable="true">on</string>
    <string name="pref_camera2_shutter_sound_value_off" translatable="true">off</string>

    <string name="pref_camera2_shutter_sound_entry_on" translatable="true">@string/pref_camera_shuttersound_entry_enable</string>
    <string name="pref_camera2_shutter_sound_entry_off" translatable="true">@string/pref_camera_shuttersound_entry_disable</string>
    <string name="pref_camera2_shutter_sound_title" translatable="true">@string/pref_camera_shuttersound_title</string>

    <string name="pref_camera2_touch_track_focus_default" translatable="true">off</string>
    <string name="pref_camera2_touch_track_focus_value_on" translatable="true">on</string>
    <string name="pref_camera2_touch_track_focus_value_off" translatable="true">off</string>

    <string name="pref_camera2_touch_track_focus_entry_on" translatable="true">Enable</string>
    <string name="pref_camera2_touch_track_focus_off" translatable="true">Disable</string>
    <string name="pref_camera2_touch_track_focus_title" translatable="true">Touch Track Focus</string>

    <string name="pref_camera2_timer_entry_off" translatable="true">Off</string>
    <string name="pref_camera2_timer_entry_2sec" translatable="true">2 seconds</string>
    <string name="pref_camera2_timer_entry_5sec" translatable="true">5 seconds</string>
    <string name="pref_camera2_timer_entry_10sec" translatable="true">10 seconds</string>

    <string name="pref_camera2_timer_value_off" translatable="false">0</string>
    <string name="pref_camera2_timer_value_2sec" translatable="false">2</string>
    <string name="pref_camera2_timer_value_5sec" translatable="false">5</string>
    <string name="pref_camera2_timer_value_10sec" translatable="false">10</string>
    <string name="pref_camera2_saveraw_default">disable</string>
    <string name="pref_camera2_saveraw_title">Save with RAW format</string>
    <string name="pref_camera2_saveraw_value_disable" translatable="false">disable</string>
    <string name="pref_camera2_saveraw_value_enable" translatable="false">enable</string>

    <string name="pref_camera2_saveraw_entry_disable">disable</string>
    <string name="pref_camera2_saveraw_entry_enable">enable</string>

    <string name="panocapture_direction_is_not_determined" translatable="true">Move constantly in one direciton.</string>
    <string name="panocapture_intro" translatable="true">Press camera button to start.</string>
    <string name="panocapture_direction_is_changed" translatable="true">Direction is changed. Stopping now...</string>
    <string name="makeup_ui_title" translatable="true">Please choose the strength of the beautification.</string>
    <string name="makeup_ui_ok_button" translatable="true">OK</string>
    <string name="makeup_seekbar_low" translatable="true">low</string>
    <string name="makeup_seekbar_high" translatable="true">high</string>

    <string name="perf_camera2_version_info" translatable="true">Version Info</string>
    <string name="perf_camera2_preferences_category_general" translatable="true">General</string>
    <string name="perf_camera2_preferences_category_photo" translatable="true">Still camera</string>
    <string name="perf_camera2_preferences_category_video" translatable="true">Video camera</string>
    <string name="perf_camera2_preferences_category_system" translatable="true">System</string>

    <string name="pref_camera2_scene_mode_dual_camera_instructional_content" translatable="true">In dual camera mode, your pictures will be clearer. You can also apply background effects, by editing your dual camera pictures in the Gallery.</string>
    <string name="pref_camera2_scene_mode_ubi_focus_instructional_content"  translatable="true">With UbiFocus, your camera will take five pictures and combine them into one picture,where everything is in focus. You can choose to re-focus, after you take the picture.</string>
    <string name="pref_camera2_scene_mode_opti_zoom_instructional_content"  translatable="true">Use Optizoom when you want to zoom in for a clear picture.You can zoom before, or after taking the picture.</string>
    <string name="pref_camera2_scene_mode_sports_instructional_content"  translatable="true">Sports mode helps you take better sports or action shots.\n\nImporves your photos, but has no effect on video recording.</string>
    <string name="pref_camera2_scene_mode_hdr_instructional_content"  translatable="true">When you are in mixed lighting conditions such as low light and backlit scenes select "HDR" for best possible pictures.</string>
    <string name="pref_camera2_scene_mode_best_photo_instructional_content"  translatable="true">Allows you to take a burst of 10 photos, and you can choose the best one(s).</string>
    <string name="pref_camera2_scene_mode_chroma_flash_instructional_content"  translatable="true">With ChromaFlash, the lighting in your flash photos will be optimized for all of the people and objects in your pictures.This feature applies only to the back camera.</string>
    <string name="pref_camera2_scene_mode_sharp_photo_instructional_content"  translatable="true">Sharp Phot reduces blur that is due to motion in the scene, or due to hand motion.</string>
    <string name="pref_camera2_scene_mode_tracking_focus_instructional_content"  translatable="true">Allows you to track and focus a selected person or object while taking pictures or videos. To start, position the focus rectangle on a person or object you want to track.</string>
    <string name="pref_camera2_scene_mode_time_lapse_instructional_content"  translatable="true">With Time Lapse, you can transform a long video recording into a few seconds of video.This effect applies to videos, but not photos.</string>
    <string name="pref_camera2_scene_mode_panorama_instructional_content"  translatable="true">Allows you to pan left or right to take a wide landscape photo. You can alse pan up or down to take tall photos.</string>
    <string name="pref_camera2_scene_mode_blur_buster_instructional_content"  translatable="true">BlurBuster reduces blur from shaky hands.It can be helpful when taking photos in difficult places.</string>
    <string name="pref_camera2_scene_mode_pro_instructional_content"  translatable="true">With Pro Mode, you can manually control settings for ISO,Exposure, White Balance, and Focus. You will have easy access to all of these advanced settings</string>
    <string name="pref_camera2_scene_mode_deepzoom_instructional_content"  translatable="true">With DeepZoom Mode, you can use the 2X or 4X to take picture, then you can get the deepzoom`s picture </string>
    <string name="pref_camera2_scene_mode_deepportrait_instructional_content" translatable="true">With DeepPortrait, you can take selfies, with a blurred background. You can use the slider to adjust the amount of the blur</string>
    <string name="pref_camera2_not_show_again">Do not show again</string>
    <string name="pref_camera2_scene_mode_instructional_ok" translatable="true">OK</string>

    <string name="pref_camera2_instant_aec_title" translatable="true">Instant AEC</string>
    <string name="pref_camera2_instant_aec_default" translatable="false">0</string>
    <string name="pref_camera2_instant_aec_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera2_instant_aec_entry_aggressive" translatable="true">Aggressive AEC</string>
    <string name="pref_camera2_instant_aec_entry_fast" translatable="true">Fast AEC</string>

    <string name="pref_camera2_saturation_level_title" translatable="true">Saturation Level</string>
    <string name="pref_camera2_saturation_level_default" translatable="false">5</string>
    <string name="pref_camera2_saturation_level_entry_0" translatable="true">Level 0</string>
    <string name="pref_camera2_saturation_level_entry_1" translatable="true">Level 1</string>
    <string name="pref_camera2_saturation_level_entry_2" translatable="true">Level 2</string>
    <string name="pref_camera2_saturation_level_entry_3" translatable="true">Level 3</string>
    <string name="pref_camera2_saturation_level_entry_4" translatable="true">Level 4</string>
    <string name="pref_camera2_saturation_level_entry_5" translatable="true">Level 5(Default)</string>
    <string name="pref_camera2_saturation_level_entry_6" translatable="true">Level 6</string>
    <string name="pref_camera2_saturation_level_entry_7" translatable="true">Level 7</string>
    <string name="pref_camera2_saturation_level_entry_8" translatable="true">Level 8</string>
    <string name="pref_camera2_saturation_level_entry_9" translatable="true">Level 9</string>
    <string name="pref_camera2_saturation_level_entry_10" translatable="true">Level 10</string>

    <string name="pref_camera2_anti_banding_level_title" translatable="true">Anti Banding Level</string>
    <string name="pref_camera2_anti_banding_level_default" translatable="false">3</string>
    <string name="pref_camera2_anti_banding_level_entry_off" translatable="true">Off</string>
    <string name="pref_camera2_anti_banding_level_entry_50hz" translatable="true">50HZ</string>
    <string name="pref_camera2_anti_banding_level_entry_60hz" translatable="true">60HZ</string>
    <string name="pref_camera2_anti_banding_level_entry_auto" translatable="true">Auto</string>

    <string name="pref_camera2_hdr_title" translatable="true">HDR &amp; Non-HDR</string>
    <string name="pref_camera2_hdr_default" translatable="false">disable</string>
    <string name="pref_camera2_hdr_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_hdr_entry_disable" translatable="true">Disable</string>

    <string name="pref_camera2_auto_hdr_title" translatable="true">AUTO HDR</string>
    <string name="pref_camera2_auto_hdr_default" translatable="false">disable</string>
    <string name="pref_camera2_auto_hdr_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_auto_hdr_entry_disable" translatable="true">Disable</string>

    <string name="pref_camera_bokeh_mode_default" translatable="false">0</string>
    <string name="pref_camera_bokeh_mode_title" translatable="true">Bokeh Mode</string>
    <string name="pref_camera_bokeh_mode_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera_bokeh_mode_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera_bokeh_mode_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera_bokeh_mode_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera_bokeh_mpo_default" translatable="false">0</string>
    <string name="pref_camera_bokeh_mpo_title" translatable="true">Bokeh Mode MPO</string>
    <string name="pref_camera_bokeh_mpo_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera_bokeh_mpo_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera_bokeh_mpo_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera_bokeh_mpo_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera_bokeh_blur_degree_default" translatable="false">50</string>
    <string name="pref_camera_bokeh_blur_degree_title" translatable="true">Bokeh Blur Value</string>
    <!-- Default Zoom setting. -->
    <string name="pref_camera_zoom_default" translatable="false">0</string>
    <string name="pref_camera_zoom_title">Zoom</string>
    <!-- Settings menu, Zoom choices -->
    <string name="pref_camera_zoom_entry_off">Off</string>
    <string name="pref_camera_zoom_entry_1x">1x</string>
    <string name="pref_camera_zoom_entry_2x">2x</string>
    <string name="pref_camera_zoom_entry_3x">3x</string>
    <string name="pref_camera_zoom_entry_4x">4x</string>
    <string name="pref_camera_zoom_entry_5x">5x</string>
    <string name="pref_camera_zoom_entry_6x">6x</string>
    <string name="pref_camera_zoom_entry_7x">7x</string>
    <string name="pref_camera_zoom_entry_8x">8x</string>
    <string name="pref_camera_zoom_entry_9x">9x</string>
    <string name="pref_camera_zoom_entry_10x">10x</string>
    <string name="pref_camera2_fd_smile_title" translatable="true">FD Smile Detection</string>
    <string name="pref_camera2_fd_gaze_title" translatable="true">FD Gaze Detection</string>
    <string name="pref_camera2_fd_blink_title" translatable="true">FD Blink Detection</string>
    <string name="pref_camera2_facial_contour_title" translatable="true">FD Facial contour</string>
    <string name="pref_camera2_face_points_title" translatable="true">FD Face points</string>
    <string name="pref_camera2_face_detection_mode_title" translatable="true">FD Face detection mode</string>
    <string name="pref_camera2_bsgc_default" translatable="false">disable</string>
    <string name="pref_camera2_bsgc_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_bsgc_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera2_bsgc_entry_value_enable" translatable="false">enable</string>
    <string name="pref_camera2_bsgc_entry_value_disable" translatable="false">disable</string>
    <string name="pref_camera2_facial_contour_default" translatable="false">disable</string>

    <string name="pref_camera2_face_points_default" translatable="false">disable</string>
    <string name="pref_camera2_face_detection_mode_default" translatable="false">1</string>
    <string name="pref_camera2_face_detection_entry_simple" translatable="true">Simple</string>
    <string name="pref_camera2_face_detection_entry_full" translatable="true">Full</string>
    <string name="pref_camera2_face_detection_entry_value_simple" translatable="false">1</string>
    <string name="pref_camera2_face_detection_entry_value_full" translatable="false">2</string>


    <string name="pref_camera2_picturesize_default" translatable="false">3840x2160</string>
    <string name="pref_camera2_video_quality_default" translatable="false">1920x1080</string>

    <string name="pref_camera2_zsl_title" translatable="true">ZSL</string>
    <string name="pref_camera2_zsl_default" translatable="false">@string/pref_camera2_zsl_entryvalue_hal_zsl</string>
    <string name="pref_camera2_zsl_entry_disable" translatable="true">Off</string>
    <string name="pref_camera2_zsl_entry_app_zsl" translatable="true">APP-ZSL</string>
    <string name="pref_camera2_zsl_entry_hal_zsl" translatable="true">HAL-ZSL</string>
    <string name="pref_camera2_zsl_entryvalue_disable" translatable="false">disable</string>
    <string name="pref_camera2_zsl_entryvalue_app_zsl" translatable="false">app-zsl</string>
    <string name="pref_camera2_zsl_entryvalue_hal_zsl" translatable="false">hal-zsl</string>

    <string name="pref_camera2_videoencoderprofile_title" translatable="true">EncoderProfile</string>
    <string name="pref_camera2_videoencoderprofile_default">off</string>

    <string name="pref_camera2_sharpness_control_title" translatable="true">SharpnessControl</string>
    <string name="pref_camera2_sharpness_control_default" translatable="true">2</string>
    <string name="pref_camera2_sharpness_control_entry_level0" translatable="false">Level 0</string>
    <string name="pref_camera2_sharpness_control_entry_level1" translatable="false">Level 1</string>
    <string name="pref_camera2_sharpness_control_entry_level2" translatable="false">Level 2 (Default)</string>
    <string name="pref_camera2_sharpness_control_entry_level3" translatable="false">Level 3</string>
    <string name="pref_camera2_sharpness_control_entry_level4" translatable="false">Level 4</string>
    <string name="pref_camera2_sharpness_control_entry_level5" translatable="false">Level 5</string>
    <string name="pref_camera2_sharpness_control_entry_level6" translatable="false">Level 6</string>

    <string name="pref_camera2_afmode_default" translatable="false">-1</string>
    <string name="pref_camera2_afmode_title">Set CONTROL_AF_MODE</string>
    <string name="pref_camera2_afmode_entry_disable">Disable</string>
    <string name="pref_camera2_afmode_entry_off">Off</string>
    <string name="pref_camera2_afmode_entry_auto">Auto</string>
    <string name="pref_camera2_afmode_entry_macro">Macro</string>
    <string name="pref_camera2_afmode_entry_continuous_video">Continuous Video</string>
    <string name="pref_camera2_afmode_entry_continuous_picture">Continuous Picture</string>
    <string name="pref_camera2_afmode_entry_edof">EDOF</string>
    <string name="pref_camera2_afmode_value_disable" translatable="false">-1</string>
    <string name="pref_camera2_afmode_value_off" translatable="false">0</string>
    <string name="pref_camera2_afmode_value_auto" translatable="false">1</string>
    <string name="pref_camera2_afmode_value_macro" translatable="false">2</string>
    <string name="pref_camera2_afmode_value_continuous_video" translatable="false">3</string>
    <string name="pref_camera2_afmode_value_continuous_picture" translatable="false">4</string>
    <string name="pref_camera2_afmode_value_edof" translatable="false">5</string>

    <string name="pref_camera2_exposure_metering_title" translatable="true">ExposureMetering</string>
    <string name="pref_camera2_exposure_metering_default" translatable="true">1</string>
    <string name="pref_camera2_exposure_metering_entry_frame_average" translatable="false">FrameAverage</string>
    <string name="pref_camera2_exposure_metering_entry_center_weighted" translatable="false">CenterWeighted</string>
    <string name="pref_camera2_exposure_metering_entry_spot_meteting" translatable="false">SpotMeteting</string>
    <string name="pref_camera2_exposure_metering_entryvalue_frame_average" translatable="false">0</string>
    <string name="pref_camera2_exposure_metering_entryvalue_center_weighted" translatable="false">1</string>
    <string name="pref_camera2_exposure_metering_entryvalue_spot_meteting" translatable="false">2</string>

    <string name="pref_camera2_switch_camera_default" translatable="false">-1</string>
    <string name="pref_camera2_switch_camera_title" translatable="true">Switch camera by id</string>


    <string name="pref_camera2_multi_camera_mode_default" translatable="false">0</string>
    <string name="pref_camera2_multi_camera_mode_title" translatable="true">Enable multi Camera mode</string>
    <string name="pref_camera2_physical_camera_default" translatable="false"></string>
    <string name="pref_camera2_physical_camera_title" translatable="true">Enable physical camera</string>
    <string name="pref_camera2_physical_recording_title" translatable="true">Enable physical recording</string>
    <string name="pref_camera2_physical_jpeg_title" translatable="true">Enable physical JPEG callback</string>
    <string name="pref_camera2_physical_yuv_title" translatable="true">Enable physical YUV callback</string>
    <string name="pref_camera2_physical_raw_title" translatable="true">Enable physical RAW callback</string>
    <string name="pref_camera2_physical_hdr_title" translatable="true">Enable physical HDR</string>
    <string name="pref_camera2_physical_mfnr_title" translatable="true">Enable physical MFNR</string>
    <string name="pref_camera2_physical_size_title" translatable="true">Picture size of physical</string>
    <string name="pref_camera2_physical_quality_title" translatable="true">Video quality of physical</string>

    <string name="pref_camera2_single_physical_camera_default" translatable="false"></string>
    <string name="pref_camera2_single_physical_camera_title" translatable="true">Enable single physical camera</string>

    <!-- QCFA Title -->
    <string name="pref_camera2_qcfa_title" translatable="true">QCFA</string>
    <string name="pref_camera2_qcfa_default" translatable="false">disable</string>
    <!-- QCFA choices -->
    <string name="pref_camera_qcfa_value_disable">disable</string>
    <string name="pref_camera_qcfa_value_enable">enable</string>

    <string name="pref_camera2_eis_title" translatable="true">Video EIS</string>
    <string name="pref_camera2_eis_default" translatable="false">disable</string>
    <string name="pref_camera2_eis_entry_v2enable" translatable="false">V2</string>
    <string name="pref_camera2_eis_entry_v3enable" translatable="false">V3</string>
    <string name="pref_camera2_eis_entry_v3_set_when_pause" translatable="false">V3 and set when pause</string>
    <string name="pref_camera2_eis_entry_disable" translatable="false">disable</string>
    <string name="pref_camera2_eis_entry_value_v2enable" translatable="false">V2</string>
    <string name="pref_camera2_eis_entry_value_v3enable" translatable="false">V3</string>
    <string name="pref_camera2_eis_entry_value_v3_set_when_pause" translatable="false">V3SetWhenPause</string>
    <string name="pref_camera2_eis_entry_value_disable" translatable="false">disable</string>

    <string name="pref_camera2_fovc_title" translatable="true">Video FOVC</string>
    <string name="pref_camera2_fovc_default" translatable="false">false</string>
    <string name="pref_camera2_fovc_entry_enable" translatable="false">Enable</string>
    <string name="pref_camera2_fovc_entry_disable" translatable="false">Disable</string>
    <string name="pref_camera2_fovc_entry_value_enable" translatable="false">true</string>
    <string name="pref_camera2_fovc_entry_value_disable" translatable="false">false</string>

    <string name="pref_camera2_video_hdr_title" translatable="true">Video HDR</string>
    <string name="pref_camera2_video_hdr_default" translatable="false">0</string>
    <string name="pref_camera2_video_hdr_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_video_hdr_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_video_hdr_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_video_hdr_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_picture_format_title">Picture Format</string>
    <string name="pref_camera2_picture_format_default">0</string>
    <string name="pref_camera2_picture_format_entry_0">JPEG</string>
    <string name="pref_camera2_picture_format_entry_1">HEIC</string>

    <string name="pref_camera2_deepportrait_entry_value_disable" translatable="false">off</string>
    <string name="pref_camera2_deepportrait_entry_value_enable" translatable="false">on</string>
    <string name="pref_camera_scenemode_entry_deepportrait" translatable="false">Deepportrait</string>

    <string name="pref_camera2_switcher_title" translatable="false">Camera Switcher</string>
    <string name="pref_camera2_switcher_default" translatable="false">rear</string>
    <string name="pref_camera2_switcher_entry_rear" translatable="false">rear</string>
    <string name="pref_camera2_switcher_entry_front" translatable="false">front</string>
    <string name="pref_camera2_switcher_entry_value_rear" translatable="false">rear</string>
    <string name="pref_camera2_switcher_entry_value_front" translatable="false">front</string>

    <string name="pref_camera2_physical_camera_entries_defualt"  translatable="false">Disable</string>
    <string name="pref_camera2_physical_camera_entry_value_defualt"  translatable="false">logical</string>

    <string name="pref_camera2_force_aux_title" translatable="false">Force AUX</string>
    <string name="pref_camera2_force_aux_default" translatable="false">off</string>
    <string name="pref_camera2_force_aux_entry_on" translatable="false">on</string>
    <string name="pref_camera2_force_aux_entry_off" translatable="false">off</string>
    <string name="pref_camera2_force_aux_entry_value_on" translatable="false">on</string>
    <string name="pref_camera2_force_aux_entry_value_off" translatable="false">off</string>

    <string name="pref_camera2_oncapturebufferlost_title" translatable="false">onCaptureBufferLost hint</string>
    <string name="pref_camera2_oncapturebufferlost_default" translatable="false">off</string>
    <string name="pref_camera2_oncapturebufferlost_entry_on" translatable="false">on</string>
    <string name="pref_camera2_oncapturebufferlost_entry_off" translatable="false">off</string>
    <string name="pref_camera2_oncapturebufferlost_entry_value_on" translatable="false">on</string>
    <string name="pref_camera2_oncapturebufferlost_entry_value_off" translatable="false">off</string>

    <string name="pref_camera2_stats_visualizer_enable_title" translatable="false">Stats Visualizer Enable</string>
    <string name="pref_camera2_stats_visualizer_enable_default" translatable="false">0</string>
    <string name="pref_camera2_stats_visualizer_entry_enable" translatable="false">enable</string>
    <string name="pref_camera2_stats_visualizer_entry_disable" translatable="false">disable</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_stats_visualizer_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_stats_visualizer_title" translatable="true">Stats Visualizer</string>
    <string name="pref_camera2_stats_visualizer_default" translatable="false"></string>
    <string name="pref_camera2_stats_visualizer_entry_bg_stats" translatable="false">BG Stats</string>
    <string name="pref_camera2_stats_visualizer_entry_be_stats" translatable="false">BE Stats</string>
    <string name="pref_camera2_stats_visualizer_entry_hist_stats" translatable="false">Hist Stats</string>
    <string name="pref_camera2_stats_visualizer_entry_awb_info" translatable="false">AWB Info</string>
    <string name="pref_camera2_stats_visualizer_entry_aec_info" translatable="false">AEC Info</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable_bg" translatable="false">0</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable_be" translatable="false">1</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable_hist" translatable="false">2</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable_awb" translatable="false">3</string>
    <string name="pref_camera2_stats_visualizer_entry_value_enable_aec" translatable="false">4</string>
    <string name="stats_visualizer_bg_label">BG GraphView</string>
    <string name="stats_visualizer_be_label">BE GraphView</string>

    <string name="pref_camera2_variable_fps_title" translatable="true">DYNAMIC FPS</string>
    <string name="pref_camera2_variable_fps_default" translatable="false">0</string>
    <string name="pref_camera2_variable_fps_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_variable_fps_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_variable_fps_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_variable_fps_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_video_flip_title" translatable="true">VIDEO FLIP</string>
    <string name="pref_camera2_video_flip_default" translatable="false">0</string>
    <string name="pref_camera2_video_flip_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_video_flip_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_video_flip_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_video_flip_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_capture_mfnr_title" translatable="true">MFNR</string>
    <string name="pref_camera2_capture_mfnr_default" translatable="false">0</string>
    <string name="pref_camera2_capture_mfnr_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_capture_mfnr_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_capture_mfnr_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_capture_mfnr_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_live_preview_title" translatable="true">LivePreview</string>
    <string name="pref_camera2_live_preview_default" translatable="false">0</string>
    <string name="pref_camera2_live_preview_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_live_preview_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_live_preview_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_live_preview_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_fs2_title" translatable="true">FastShutter</string>
    <string name="pref_camera2_fs2_default" translatable="false">0</string>
    <string name="pref_camera2_fs2_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_fs2_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_fs2_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_fs2_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_abort_captures_title" translatable="true">Abort Captures</string>
    <string name="pref_camera2_abort_captures_default" translatable="false">1</string>
    <string name="pref_camera2_abort_captures_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_abort_captures_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_abort_captures_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_abort_captures_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_mfhdr_title" translatable="true">MFHDR Feature</string>
    <string name="pref_camera2_mfhdr_default" translatable="false">0</string>
    <string name="pref_camera2_mfhdr_entry_off" translatable="true">OFF</string>
    <string name="pref_camera2_mfhdr_entry_shdr" translatable="true">SHDR</string>
    <string name="pref_camera2_mfhdr_entry_mfhdr" translatable="false">MFHDR</string>
    <string name="pref_camera2_mfhdr_entry_value_off" translatable="false">0</string>
    <string name="pref_camera2_mfhdr_entry_value_shdr" translatable="false">1</string>
    <string name="pref_camera2_mfhdr_entry_value_mfhdr" translatable="false">2</string>

    <string name="pref_camera2_gc_shdr_title" translatable="true">GC SHDR</string>
    <string name="pref_camera2_gc_shdr_default" translatable="false">0</string>
    <string name="pref_camera2_gc_shdr_entry_on" translatable="true">enable</string>
    <string name="pref_camera2_gc_shdr_entry_off" translatable="false">disable</string>
    <string name="pref_camera2_gc_shdr_entry_value_on" translatable="false">1</string>
    <string name="pref_camera2_gc_shdr_entry_value_off" translatable="false">0</string>

    <string name="pref_camera2_shading_correction_title" translatable="true">Shading Correction Feature</string>
    <string name="pref_camera2_shading_correction_default" translatable="false">1</string>
    <string name="pref_camera2_shading_correction_entry_disable" translatable="true">Disable</string>
    <string name="pref_camera2_shading_correction_entry_enable" translatable="true">Enable</string>
    <string name="pref_camera2_shading_correction_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_shading_correction_entry_value_enable" translatable="false">1</string>

    <string name="pref_camera2_hvx_shdr_title" translatable="true">Enable HVX SHDR</string>
    <string name="pref_camera2_hvx_shdr_default" translatable="true">0</string>
    <string name="pref_camera2_hvx_shdr_entry_recording" translatable="true">From recording</string>
    <string name="pref_camera2_hvx_shdr_entry_preview" translatable="true">From preview</string>
    <string name="pref_camera2_hvx_shdr_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_hvx_shdr_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_hvx_shdr_entry_value_recording" translatable="false">3</string>
    <string name="pref_camera2_hvx_shdr_entry_value_preview" translatable="false">2</string>
    <string name="pref_camera2_hvx_shdr_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_hvx_shdr_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_hvx_mfhdr_title" translatable="true">Enable HVX MFHDR</string>
    <string name="pref_camera2_hvx_mfhdr_default" translatable="true">0</string>
    <string name="pref_camera2_hvx_mfhdr_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_hvx_mfhdr_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_hvx_mfhdr_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_hvx_mfhdr_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_extended_max_zoom_title" translatable="true">Extended max zoom</string>
    <string name="pref_camera2_extended_max_zoom_default" translatable="false">0</string>
    <string name="pref_camera2_extended_max_zoom_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_extended_max_zoom_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_extended_max_zoom_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_extended_max_zoom_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_swpdpc_title" translatable="true">SWPDPC</string>
    <string name="pref_camera2_swpdpc_default" translatable="false">0</string>
    <string name="pref_camera2_swpdpc_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_swpdpc_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_swpdpc_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_swpdpc_entry_value_disable" translatable="false">0</string>

    <string name="pref_camera2_burst_limit_title" translatable="true">Burst Limit</string>
    <string name="pref_camera2_burst_limit_default" translatable="false">0</string>
    <string name="pref_camera2_burst_limit_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_burst_limit_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_burst_limit_entry_value_enable" translatable="false">1</string>
    <string name="pref_camera2_burst_limit_entry_value_disable" translatable="false">0</string>


    <!-- tone mapping -->
    <string name="pref_camera_tone_mapping_default">off</string>
	<string name="pref_camera_tone_mapping_title">Tone Mapping</string>
    <!-- tone mapping entry-->
    <string name="pref_camera_tone_mapping_entry_dark_boost_offset">Dark boost offset</string>
    <string name="pref_camera_tone_mapping_entry_fourth_tone_anchor">Fourth tone anchor</string>
	<string name="pref_camera_tone_mapping_entry_user_setting">User setting</string>
    <string name="pref_camera_tone_mapping_entry_off">Off</string>
    <!-- tone mapping entry value-->
    <string name="pref_camera_tone_mapping_value_dark_boost_offset">dark_boost_offset</string>
    <string name="pref_camera_tone_mapping_value_fourth_tone_anchor">fourth_tone_anchor</string>
    <string name="pref_camera_tone_mapping_value_user_setting">user_setting</string>
    <string name="pref_camera_tone_mapping_value_off">off</string>

    <!-- select mode -->
    <string name="pref_camera2_select_mode_default">default</string>
    <string name="pref_camera2_select_mode_title">Select Mode</string>
    <!-- select mode entry-->
    <string name="pref_camera2_select_mode_entry_default">Default</string>
    <string name="pref_camera2_select_mode_entry_sat">SAT</string>
    <string name="pref_camera2_select_mode_entry_rtb">RTB</string>
    <string name="pref_camera2_select_mode_entry_single_rear">Single rear cameraID</string>
    <!-- select mode value-->
    <string name="pref_camera2_select_mode_entry_value_default">default</string>
    <string name="pref_camera2_select_mode_entry_value_sat">sat</string>
    <string name="pref_camera2_select_mode_entry_value_rtb">rtb</string>
    <string name="pref_camera2_select_mode_entry_value_single_rear">single_rear_cameraid</string>

    <string name="pref_camera2_master_cb_title" translatable="true">Enable Master Snapshot</string>
    <string name="pref_camera2_master_cb_default" translatable="false">0</string>
    <string name="pref_camera2_master_cb_entry_disable" translatable="true">primary</string>
    <string name="pref_camera2_master_cb_entry_enable" translatable="true">master</string>
    <string name="pref_camera2_master_cb_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_master_cb_entry_value_enable" translatable="false">1</string>

    <string name="pref_camera2_raw_cb_info_title" translatable="true">Mcx Raw Callback Info</string>
    <string name="pref_camera2_raw_cb_info_default" translatable="false">0</string>
    <string name="pref_camera2_raw_cb_info_entry_default" translatable="true">McxCbModeDefault</string>
    <string name="pref_camera2_raw_cb_info_entry_primary" translatable="true">McxCbModePrimaryOnly</string>
    <string name="pref_camera2_raw_cb_info_entry_master" translatable="false">McxCbModeMasterOnly</string>
    <string name="pref_camera2_raw_cb_info_entry_all_active" translatable="false">McxCbModeAllActiveStreams</string>
    <string name="pref_camera2_raw_cb_info_entry_logical_master" translatable="true">McxCbModeLogicalMasterSlave</string>
    <string name="pref_camera2_raw_cb_info_entry_value_default" translatable="true">0</string>
    <string name="pref_camera2_raw_cb_info_entry_value_primary" translatable="false">1</string>
    <string name="pref_camera2_raw_cb_info_entry_value_master" translatable="false">2</string>
    <string name="pref_camera2_raw_cb_info_entry_value_all_active" translatable="true">3</string>
    <string name="pref_camera2_raw_cb_info_entry_value_logical_master" translatable="true">4</string>

    <!-- stats nn control value-->
    <string name="pref_camera2_statsnn_control_title" translatable="true">Stats NN Control</string>
    <string name="pref_camera2_statsnn_control_default" translatable="false">0</string>
    <string name="pref_camera2_statsnn_control_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_statsnn_control_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_statsnn_control_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_statsnn_control_entry_value_enable" translatable="false">1</string>

    <!-- QLL value-->
    <string name="pref_camera2_qll_title" translatable="true">QLL</string>
    <string name="pref_camera2_qll_default" translatable="false">0</string>
    <string name="pref_camera2_qll_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_qll_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_qll_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_qll_entry_value_enable" translatable="false">1</string>

    <!-- raw reprocess type-->
    <string name="pref_camera2_raw_reprocess_default">0</string>
    <string name="pref_camera2_raw_reprocess_title">Raw Reprocess Callback Type</string>
    <string name="pref_camera2_raw_reprocess_entry_off">off</string>
    <string name="pref_camera2_raw_reprocess_entry_yuv">YUV</string>
    <string name="pref_camera2_raw_reprocess_entry_jpeg">JPEG</string>
    <string name="pref_camera2_raw_reprocess_entry_heic">HEIC</string>
    <string name="pref_camera2_raw_reprocess_entry_all_yuv">ALL2YUV</string>
    <string name="pref_camera2_raw_reprocess_entry_all_jpeg">ALL2JPEG</string>
    <string name="pref_camera2_raw_reprocess_entry_value_off">0</string>
    <string name="pref_camera2_raw_reprocess_entry_value_yuv">1</string>
    <string name="pref_camera2_raw_reprocess_entry_value_jpeg">2</string>
    <string name="pref_camera2_raw_reprocess_entry_value_heic">3</string>
    <string name="pref_camera2_raw_reprocess_entry_value_all_yuv">4</string>
    <string name="pref_camera2_raw_reprocess_entry_value_all_jpeg">5</string>

    <!-- raw reprocess type-->
    <string name="pref_camera2_raw_format_default">10</string>
    <string name="pref_camera2_raw_format_title">Raw Format Type</string>
    <string name="pref_camera2_raw_format_entry_10">Raw10</string>
    <string name="pref_camera2_raw_format_entry_16">Raw16</string>
    <string name="pref_camera2_raw_format_entry_value_10">10</string>
    <string name="pref_camera2_raw_format_entry_value_16">16</string>

    <!-- rawinfo type-->
    <string name="pref_camera2_rawinfo_type_default">0</string>
    <string name="pref_camera2_rawinfo_type_title">Raw info Type</string>
    <string name="pref_camera2_rawinfo_type_entry_mipi">mipiraw</string>
    <string name="pref_camera2_rawinfo_type_entry_ife_ideal">IFE ideal raw</string>
    <string name="pref_camera2_rawinfo_type_entry_bps_ideal">BPS Ideal raw</string>
    <string name="pref_camera2_rawinfo_type_entry_value_mipi">0</string>
    <string name="pref_camera2_rawinfo_type_entry_value_ife_ideal">1</string>
    <string name="pref_camera2_rawinfo_type_entry_value_bps_ideal">2</string>

    <string name="lock_af_ae_label">AE/AF Lock</string>
    <!-- ai denoiser value-->
    <string name="pref_camera2_ai_denoiser_title" translatable="true">AI Denoiser</string>
    <string name="pref_camera2_ai_denoiser_default" translatable="false">0</string>
    <string name="pref_camera2_ai_denoiser_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_ai_denoiser_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_ai_denoiser_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_ai_denoiser_entry_value_enable" translatable="false">1</string>
    <!-- ai denoiser format value-->
    <string name="pref_camera2_ai_denoiser_format_title" translatable="true">AI Denoiser Format</string>
    <string name="pref_camera2_ai_denoiser_format_default" translatable="false">1</string>
    <string name="pref_camera2_ai_denoiser_format_entry_nv12" translatable="true">AIDE_NV12</string>
    <string name="pref_camera2_ai_denoiser_format_entry_nv21" translatable="true">AIDE_NV21</string>
    <string name="pref_camera2_ai_denoiser_format_entry_value_nv12" translatable="false">0</string>
    <string name="pref_camera2_ai_denoiser_format_entry_value_nv21" translatable="false">1</string>
    <!-- ai denoiser mode value-->
    <string name="pref_camera2_ai_denoiser_mode_title" translatable="true">AI Denoiser Mode</string>
    <string name="pref_camera2_ai_denoiser_mode_default" translatable="false">1</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_aide1" translatable="true">AIDE1</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_aide2" translatable="true">AIDE2</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_aide_sat" translatable="true">AIDE2_SAT</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_value_aide1" translatable="false">0</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_value_aide2" translatable="false">1</string>
    <string name="pref_camera2_ai_denoiser_mode_entry_value_aide_sat" translatable="false">2</string>

    <!-- inSensor Zoom value-->
    <string name="pref_camera2_insensor_zoom_title" translatable="true">InSensor Zoom</string>
    <string name="pref_camera2_insensor_zoom_default" translatable="false">0</string>
    <string name="pref_camera2_insensor_zoom_entry_disable" translatable="true">disable</string>
    <string name="pref_camera2_insensor_zoom_entry_enable" translatable="true">enable</string>
    <string name="pref_camera2_insensor_zoom_entry_value_disable" translatable="false">0</string>
    <string name="pref_camera2_insensor_zoom_entry_value_enable" translatable="false">1</string>

    <string name="pref_camera2_3A_debug_info_title">3A Debug Info</string>
    <string name="pref_camera2_3A_debug_info_default" translatable="false">0</string>
</resources>

