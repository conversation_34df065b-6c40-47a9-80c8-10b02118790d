<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<!-- Camera app resources that may need to be customized
     for different hardware or product builds. -->
<resources>
    <!-- Maximum recording length in milliseconds. 0 means unlimited. -->
    <integer name="max_video_recording_length">0</integer>

    <!-- This value may be tweaked to save memory on low RAM devices. The value
         is the percentage of camera preview height/width to scale to. -->
    <integer name="panorama_frame_size_reduction">90</integer>

    <!-- This value may be changed to true to enable the warped pano preview overlayed on top
         of the fullscreen pano preview. -->
    <bool name="enable_warped_pano_preview">true</bool>
</resources>
