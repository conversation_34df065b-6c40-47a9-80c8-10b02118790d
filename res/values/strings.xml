<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2007 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- The name of the Android Camera application. [CHAR LIMIT=16] -->
    <string name="app_name">Camera</string>
    <string name="snapcam_app_name">Snapdragon Camera</string>
    <!-- The label for the Video Camera activity. [CHAR LIMIT=16] -->
    <string name="video_camera_label">Camcorder</string>
    <!-- Used to format short video duration in Details dialog. minutes:seconds e.g. 00:30 -->
    <string name="details_ms">%1$02d:%2$02d</string>
    <!-- Used to format video duration in Details dialog. hours:minutes:seconds e.g. 0:21:30 -->
    <string name="details_hms">%1$d:%2$02d:%3$02d</string>

    <!-- Displayed in the title of the dialog for things to do with a picture
             that is to be "set as" (e.g. set as contact photo or set as wallpaper) -->
    <string name="set_image">Set picture as</string>

    <!-- Details dialog "OK" button. Dismisses dialog. -->
    <string name="delete">Delete</string>
    <!-- String Delete the selected media item(s) [CHAR LIMIT=50] -->
    <plurals name="delete_selection">
        <item quantity="one">Delete selected item?</item>
        <item quantity="other">Delete selected items?</item>
    </plurals>
    <string name="share">Share</string>
    <string name="share_panorama">Share panorama</string>
    <string name="share_as_photo">Share as photo</string>

    <!-- The label shown after an image is deleted [CHAR LIMIT=16] -->
    <string name="deleted">Deleted</string>

    <!-- The label on the button which when clicked will undo a deletion of image [CHAR LIMIT=16]-->
    <string name="undo">UNDO</string>

    <string name="details">Details</string>
    <string name="close">Close</string>

    <!-- String indicating how many media item(s) is(are) selected
            eg. 1 selected [CHAR LIMIT=30] -->
    <plurals name="number_of_items_selected">
        <item quantity="zero">%1$d selected</item>
        <item quantity="one">%1$d selected</item>
        <item quantity="other">%1$d selected</item>
    </plurals>

    <!-- String indicating how many media album(s) is(are) selected
            eg. 1 selected [CHAR LIMIT=30] -->
    <plurals name="number_of_albums_selected">
        <item quantity="zero">%1$d selected</item>
        <item quantity="one">%1$d selected</item>
        <item quantity="other">%1$d selected</item>
    </plurals>

    <!-- String indicating how many media group(s) is(are) selected
            eg. 1 selected [CHAR LIMIT=30] -->
    <plurals name="number_of_groups_selected">
        <item quantity="zero">%1$d selected</item>
        <item quantity="one">%1$d selected</item>
        <item quantity="other">%1$d selected</item>
    </plurals>

    <!-- String indicating timestamp of photo or video -->
    <string name="show_on_map">Show on map</string>
    <string name="rotate_left">Rotate left</string>
    <string name="rotate_right">Rotate right</string>

    <!-- String used as a menu label. The user can choose to edit the image
         [CHAR_LIMIT=20]-->
    <string name="edit">Edit</string>

    <!-- The title of the menu item to let user crop the image. [CHAR LIMIT=15] -->
    <string name="crop_action">Crop</string>
    <!-- The title of the menu item to let user trim the video. [CHAR LIMIT=15] -->
    <string name="trim_action">Trim</string>
    <!-- The title of the menu item to let user set the image as background etc. [CHAR LIMIT=15] -->
    <string name="set_as">Set as</string>

    <!-- String indicating an error when playing the video. [CHAR LIMIT=30] -->
    <string name="video_err">Can\'t play video.</string>

    <!-- Toast message shown when the cropped image has been saved in the
         %s folder (string: folder_download) [CHAR LIMIT=50]-->
    <string name="crop_saved">
        Cropped image saved to <xliff:g id="folder_name">%s</xliff:g>.</string>

    <!-- Text indicating the title of a media item in details window [CHAR LIMIT=14] -->
    <string name="title">Title</string>
    <!-- Text indicating the description of a media item in details window [CHAR LIMIT=14] -->
    <string name="description">Description</string>
    <!-- Text indicating the creation time of a media item in details window [CHAR LIMIT=14] -->
    <string name="time">Time</string>
    <!-- Text indicating the location of a media item in details window [CHAR LIMIT=14] -->
    <string name="location">Location</string>
    <!-- Text indicating the path of a media item in details window [CHAR LIMIT=14] -->
    <string name="path">Path</string>
    <!-- Text indicating the width of a media item in details window [CHAR LIMIT=14] -->
    <string name="width">Width</string>
    <!-- Text indicating the height of a media item in details window [CHAR LIMIT=14] -->
    <string name="height">Height</string>
    <!-- Text indicating the orientation of a media item in details window [CHAR LIMIT=14] -->
    <string name="orientation">Orientation</string>
    <!-- Text indicating the duration of a video item in details window [CHAR LIMIT=18] -->
    <string name="duration">Duration</string>
    <!-- Text indicating the mime type of a media item in details window [CHAR LIMIT=14] -->
    <string name="mimetype">MIME type</string>
    <!-- Text indicating the file size of a media item in details window [CHAR LIMIT=14] -->
    <string name="file_size">File size</string>
    <!-- Text indicating the maker of a media item in details window [CHAR LIMIT=14] -->
    <string name="maker">Maker</string>
    <!-- Text indicating the model of a media item in details window [CHAR LIMIT=14] -->
    <string name="model">Model</string>
    <!-- Text indicating flash info of a media item in details window [CHAR LIMIT=14] -->
    <string name="flash">Flash</string>
    <!-- Text indicating aperture of a media item in details window [CHAR LIMIT=14] -->
    <string name="aperture">Aperture</string>
    <!-- Text indicating the focal length of a media item in details window [CHAR LIMIT=14] -->
    <string name="focal_length">Focal Length</string>
    <!-- Text indicating the white balance of a media item in details window [CHAR LIMIT=18] -->
    <string name="white_balance">White balance</string>
    <!-- Text indicating the exposure time of a media item in details window [CHAR LIMIT=14] -->
    <string name="exposure_time">Exposure time</string>
    <!-- Text indicating the ISO speed rating of a media item in details window [CHAR LIMIT=14] -->
    <string name="iso">ISO</string>
    <!-- String indicating the time units in seconds. [CHAR LIMIT=8] -->
    <!-- String indicating the length units in milli-meters. [CHAR LIMIT=8] -->
    <string name="unit_mm">mm</string>
    <!-- String indicating how camera shooting feature is used. [CHAR LIMIT=12] -->
    <string name="manual">Manual</string>
    <!-- String indicating how camera shooting feature is used. [CHAR LIMIT=12 -->
    <string name="auto">Auto</string>
    <!-- String indicating camera flash is fired. [CHAR LIMIT=14] -->
    <string name="flash_on">Flash fired</string>
    <!-- String indicating camera flash is not used. [CHAR LIMIT=14] -->
    <string name="flash_off">No flash</string>

    <!-- String indicating image width or height is unknown. [CHAR LIMIT=14] -->
    <string name="unknown">Unknown</string>

    <!-- Toast message shown after we try to make a local album available offline
         [CHAR LIMIT=150] -->
    <string name="try_to_set_local_album_available_offline">
        This item is stored locally and available offline.</string>


    <!-- The content of a dialog showing trimming in progress. [CHAR LIMIT=30] -->
    <string name="please_wait">Please wait</string>

    <!-- Text to show with progress bar while stitching a photo sphere. 'Photo sphere' is a product name. [CHAR LIMIT=35]-->
    <string name="rendering_photo_sphere">Rendering photo sphere</string>

    <!-- title for the dialog showing the error of camera hardware -->
    <string name="camera_error_title">Camera error</string>

    <!-- message for the dialog showing the error of camera hardware -->
    <string name="cannot_connect_camera">Can\'t connect to the camera.</string>

    <!-- message for the dialog showing the camera is disabled because of security policies. Camera cannot be used. -->
    <string name="camera_disabled">Camera has been disabled because of security policies.</string>

    <!-- message for the dialog showing that the app does not have sufficient permissions [CHAR LIMIT=NONE] -->
    <string name="error_permissions">The app does not have critical permissions needed to run. Please check your permissions settings.</string>

    <!-- Dialog "Dismiss" button. Closes the dialog [CHAR LIMIT=12]-->
    <string name="dialog_dismiss">Dismiss</string>

    <!-- alert to the user to wait for some operation to complete -->
    <string name="wait">Please wait\u2026</string>

    <!-- alert to the user that an SD card must be installed before using the camera -->
    <string name="no_storage" product="default">Insert an SD card before using the camera.</string>

    <!-- alert to the user that the SD card is being disk-checked -->
    <string name="preparing_sd" product="default">Preparing SD card\u2026</string>

    <!-- alert to the user that the camera fails to read or write the SD card. -->
    <string name="access_sd_fail" product="default">Couldn\'t access SD card.</string>

    <!-- A label that overlays on top of the preview frame to indicate the camcorder is in time lapse mode [CHAR LIMIT=35] -->
    <string name="time_lapse_title">Time lapse recording</string>

    <!-- Screen display message during image capture to indicate that the capture is in progress, like during HDR+. [CHAR LIMIT=20] -->
    <string name="capturing">Capturing</string>

    <!-- Settings screen, camera selection dialog title. Users can select a camera from the phone (front-facing or back-facing). [CHAR LIMIT=20] -->
    <string name="pref_camera_id_title">Choose camera</string>

    <string name="pref_camera_id_default" translatable="false">0</string>

    <!-- In select camera setting, back facing camera. [CHAR LIMIT=14] -->
    <string name="pref_camera_id_entry_back">Back</string>
    <!-- In select camera setting, front-facing camera. [CHAR LIMIT=14] -->
    <string name="pref_camera_id_entry_front">Front</string>

    <!-- Settings screen, setting title text -->
    <string name="pref_camera_recordlocation_title">GPS location</string>

    <string name="pref_camera_recordlocation_default" translatable="false">none</string>

    <!--  Label for record location preference [CHAR LIMIT=50] -->
    <string name="pref_camera_location_label">LOCATION</string>

    <!-- Title for countdown timer on camera settings screen [CHAR LIMIT=30]-->
    <string name="pref_camera_timer_title">Countdown timer</string>

    <string name="pref_camera_timer_default" translatable="false">0</string>
    <!-- Entry for countdown timer setting. e.g. 1 second, 10 seconds, etc. [CHAR LIMIT=30]-->
    <plurals name="pref_camera_timer_entry">
        <item quantity="one">1 second</item>
        <item quantity="other">%d seconds</item>
    </plurals>
    <string name="pref_camera_timer_sound_default">@string/setting_on_value</string>
    <!-- Text followed by a checkbox to turn on/off sound effects during the countdown. [CHAR LIMIT = 24]-->
    <string name="pref_camera_timer_sound_title">Beep during countdown</string>

    <!-- Entry of camera save path -->
    <string name="pref_camera_savepath_title">Storage</string>
    <string name="pref_camera_savepath_default">0</string>
    <string name="pref_camera_savepath_entry_0">Phone</string>
    <string name="pref_camera_savepath_entry_1">SD Card</string>

    <!-- Entry of a on/off setting. The setting is turned off. [CHAR LIMIT=15] -->
    <string name="setting_off">Off</string>
    <!-- Entry of a on/off setting. The setting is turned on. [CHAR LIMIT=15] -->
    <string name="setting_on">On</string>

    <!-- The value of a camera preference indicating the setting is off. -->
    <string name="setting_off_value" translatable="false">off</string>
    <!-- The value of a camera preference indicating the setting is on. -->
    <string name="setting_on_value" translatable="false">on</string>

    <!-- The Video quality settings in preference [CHAR LIMIT=21] -->
    <string name="pref_video_quality_title">Video quality</string>
    <!-- The default quality value is NULL -->
    <string name="pref_video_quality_default" translatable="false">1920x1080</string>
    <!-- Video quality setting entry.
    Videos will be recorded in 8k DCI quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_8k" translatable="false">
    8k</string>
    <!-- Video quality setting entry.
    Videos will be recorded in 4k DCI quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_4kdci" translatable="false">
    4k DCI</string>
    <!-- Video quality setting entry.
    Videos will be recorded in 4k UHD quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_2160p" translatable="false">
    4k UHD</string>
    <!-- Video quality setting entry.
    Videos will be recorded in 4k UHD quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_4kuhd" translatable="false">
    4k UHD</string>
    <!-- Video quality setting entry. Videos will be recorded in 1080p quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_1080p" translatable="false">HD 1080p</string>
    <!-- Video quality setting entry. Videos will be recorded in 720p quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_720p" translatable="false">HD 720p</string>
    <!-- Video quality setting entry. Videos will be recorded in 480p quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_480p" translatable="false">SD 480p</string>
    <!-- Video quality setting entry. Videos will be recorded in QHD quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_qHD" translatable="false">QHD</string>
    <!-- Video quality setting entry. Videos will be recorded in 2K quality. [CHAR LIMIT=24] -->
    <string name="pref_video_quality_entry_2k" translatable="false">2K</string>

    <!-- Describes the preference dialog for choosing interval between frame capture for
    time lapse recording. Appears at top of the dialog. [CHAR LIMIT=30] -->
    <string name="pref_video_time_lapse_frame_interval_title">Time lapse</string>
    <string name="pref_video_time_lapse_frame_interval_default" translatable="false">0</string>

    <!-- Settings screen, Camera setting category title -->
    <string name="pref_camera_settings_category">Camera settings</string>

    <!-- Settings screen, Camcorder setting category title -->
    <string name="pref_camcorder_settings_category">Camcorder settings</string>

    <!-- Settings screen, Picture size title -->
    <string name="pref_camera_picturesize_title">Picture size</string>
    <!-- Settings screen, dialog choice for 48 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_256mp">256M pixels</string>
    <!-- Settings screen, dialog choice for 48 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_48mp">48M pixels</string>
    <!-- Settings screen, dialog choice for 36 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_36mp">36M pixels</string>
    <!-- Settings screen, dialog choice for 32 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_32mp">32M pixels</string>
    <!-- Settings screen, dialog choice for 28 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_28mp">28M pixels</string>
    <!-- Settings screen, dialog choice for 25 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_25mp">25M pixels</string>
    <!-- Settings screen, dialog choice for 24 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_24mp">24M pixels</string>
    <!-- Settings screen, dialog choice for 20 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_21mp">21M pixels</string>
    <!-- Settings screen, dialog choice for 16 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_16mp">16M pixels</string>
    <!-- Settings screen, dialog choice for 16 megapixels wide picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_16mp_wide">16M pixels (16:9)</string>
    <!-- Settings screen, dialog choice for 13 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_13mp">13M pixels</string>
    <!-- Settings screen, dialog choice for 12 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_12mp">12M pixels</string>
    <!-- Settings screen, dialog choice for 8 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_8mp">8M pixels</string>
    <string name="pref_camera_picturesize_entry_8mp_wide">8M pixels (16:9)</string>
    <!-- Settings screen, dialog choice for square picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_square">Square (1:1)</string>
    <!-- Settings screen, dialog choice for 5 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_5mp">5M pixels</string>
    <!-- Settings screen, dialog choice for 4 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_4mp_wide">4M pixels (16:9)</string>
    <!-- Settings screen, dialog choice for 3 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_3mp">3M pixels</string>
    <!-- Settings screen, dialog choice for 2 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_2mp">2M pixels</string>
    <!-- Settings screen, dialog choice for 2 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_2mp_wide">2M pixels (16:9)</string>
    <!-- Settings screen, dialog choice for 1.5 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_1_5mp">1.5M pixels</string>
    <!-- Settings screen, dialog choice for 1.3 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_1_3mp">1.3M pixels</string>
    <!-- Settings screen, dialog choice for 1 megapixels picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_1mp">1M pixels</string>
    <!-- Settings screen, dialog choice for VGA picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_vga">VGA</string>
    <!-- Settings screen, dialog choice for QVGA picture size [CHAR LIMIT=20] -->
    <string name="pref_camera_picturesize_entry_qvga">QVGA</string>

    <!-- Settings screen, Focus mode title -->
    <string name="pref_camera_focusmode_title">Focus mode</string>

    <!-- Settings screen, Focus mode dialog radio button choices -->
    <string name="pref_camera_focusmode_entry_auto">Auto</string>
    <!-- A setting that lets the camera focus into infinity. [CHAR LIMIT=20] -->
    <string name="pref_camera_focusmode_entry_infinity">Infinity</string>
    <!-- A setting that lets the camera operate in macro focus mode for nearby objects. [CHAR LIMIT=20] -->
    <string name="pref_camera_focusmode_entry_macro">Macro</string>

    <!-- Menu, focus mode labels [CHAR LIMIT=50] -->
    <string name="pref_camera_focusmode_label_auto">AUTO</string>
    <string name="pref_camera_focusmode_label_infinity">INFINITY</string>
    <string name="pref_camera_focusmode_label_macro">MACRO</string>

    <!-- Default flash mode setting.-->
    <string name="pref_camera_flashmode_default" translatable="false">auto</string>

    <!-- Value for flash off setting-->
    <string name="pref_camera_flashmode_no_flash" translatable="false">no_flash</string>

    <!-- Settings screen, Flash mode title -->
    <string name="pref_camera_flashmode_title">Flash</string>

    <!-- Settings screen, Flash mode 'auto'. [CHAR LIMIT=12] -->
    <string name="pref_camera_flashmode_entry_auto">Auto</string>
    <!-- Settings screen, Flash mode 'alway on'. [CHAR LIMIT=12] -->
    <string name="pref_camera_flashmode_entry_on">On</string>
    <!-- Settings screen, Flash mode 'always off'. [CHAR LIMIT=12] -->
    <string name="pref_camera_flashmode_entry_off">Off</string>

    <!-- Menu, flash mode labels [CHAR LIMIT=50] -->
    <string name="pref_camera_flashmode_label_auto">FLASH AUTO</string>
    <string name="pref_camera_flashmode_label_on">FLASH ON</string>
    <string name="pref_camera_flashmode_label_off">FLASH OFF</string>

    <!-- Default videocamera flash mode setting.-->
    <string name="pref_camera_video_flashmode_default" translatable="false">off</string>

    <!-- Default white balance setting. -->
    <string name="pref_camera_whitebalance_default" translatable="false">auto</string>

    <!-- Settings screen, white balance title -->
    <string name="pref_camera_whitebalance_title">White balance</string>
    <!-- Menu, white balance label -->
    <string name="pref_camera_whitebalance_label">WHITE BALANCE</string>

    <!-- Settings screen, White balance dialog radio button choices -->
    <string name="pref_camera_whitebalance_entry_auto">Auto</string>
    <string name="pref_camera_whitebalance_entry_incandescent">Incandescent</string>
    <string name="pref_camera_whitebalance_entry_daylight">Daylight</string>
    <string name="pref_camera_whitebalance_entry_fluorescent">Fluorescent</string>
    <string name="pref_camera_whitebalance_entry_cloudy">Cloudy</string>

    <!-- Menu, White balance labels [CHAR LIMIT=50] -->
    <string name="pref_camera_whitebalance_label_auto">AUTO</string>
    <string name="pref_camera_whitebalance_label_incandescent">INCANDESCENT</string>
    <string name="pref_camera_whitebalance_label_daylight">DAYLIGHT</string>
    <string name="pref_camera_whitebalance_label_fluorescent">FLUORESCENT</string>
    <string name="pref_camera_whitebalance_label_cloudy">CLOUDY</string>

    <!-- Default scene mode setting. -->
    <string name="pref_camera_scenemode_default" translatable="false">auto</string>

    <!-- Settings screen, Select Scene mode -->
    <string name="pref_camera_scenemode_title">Scene mode</string>

    <!-- Settings menu, scene mode choices [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_auto">Automatic</string>
    <!-- Scene mode that uses HDR plus (better HDR) [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_hdr_plus">HDR+</string>
    <!-- Scene mode that uses HDR (high dynamic range) [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_hdr">HDR</string>
    <!-- Scene mode to turn on HDR (better high dynamic range) [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_entry_turn_hdr_plus_on">HDR+ ON</string>
    <!-- Scene mode to turn off HDR (better high dynamic range) [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_entry_turn_hdr_plus_off">HDR+ OFF</string>
    <!-- Scene mode to turn on HDR (high dynamic range) [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_entry_turn_hdr_on">HDR ON</string>
    <!-- Scene mode to turn off HDR (high dynamic range) [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_entry_turn_hdr_off">HDR OFF</string>
    <!-- Scene mode that takes an image quickly with little motion blur. [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_action">Action</string>
    <!-- Scene mode that takes long exposures to capture night scenes without flash. [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_night">Night</string>
    <!-- Scene mode optimized for taking images in the sunset. [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_sunset">Sunset</string>
    <!-- Scene mode optimized for taking indoor low-lights pictures. [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_party">Party</string>
    <!-- Scene mode for refocus feature [CHAR LIMIT=16] -->
    <string name="pref_camera_scenemode_entry_refocus">UbiFocus</string>
    <string name="pref_camera_scenemode_entry_optizoom">OptiZoom</string>

    <!-- Settings menu, scene mode labels [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_label_auto">NONE</string>
    <!-- Scene mode that takes an image quickly with little motion blur. [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_label_action">ACTION</string>
    <!-- Scene mode that takes long exposures to capture night scenes without flash. [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_label_night">NIGHT</string>
    <!-- Scene mode optimized for taking images in the sunset. [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_label_sunset">SUNSET</string>
    <!-- Scene mode optimized for taking indoor low-lights pictures. [CHAR LIMIT=50] -->
    <string name="pref_camera_scenemode_label_party">PARTY</string>

    <!-- Settings menu countdown timer off [CHAR LIMIT=50] -->
    <string name="pref_camera_countdown_label_off">TIMER OFF</string>
    <!-- Settings menu countdown timer 1 second [CHAR LIMIT=50] -->
    <string name="pref_camera_countdown_label_one">1 SECOND</string>
    <!-- Settings menu countdown timer 3 seconds [CHAR LIMIT=50] -->
    <string name="pref_camera_countdown_label_three">3 SECONDS</string>
    <!-- Settings menu countdown timer 10 seconds [CHAR LIMIT=50] -->
    <string name="pref_camera_countdown_label_ten">10 SECONDS</string>
    <!-- Settings menu countdown timer 15 seconds [CHAR LIMIT=50] -->
    <string name="pref_camera_countdown_label_fifteen">15 SECONDS</string>

    <!-- Toast after trying to select a setting that is not allowed to change in scene mode [CHAR LIMIT=NONE] -->
    <string name="not_selectable_in_scene_mode">Not selectable in scene mode.</string>

    <!-- Toast after enable HDR function to inform Continuous Shot has disabled -->
    <string name="HDR_disable_continuous_shot">Enable HDR will disable Continuous Shot.</string>


    <!-- Toast after enable Advanced Capture function to inform Continuous Shot has disabled -->
    <string name="advanced_capture_disable_continuous_shot">Enable Advanced Capture will disable Continuous Shot.</string>

    <!-- Toast after enable 2k4k video quality to inform IS has disabled -->
    <string name="video_quality_4k_disable_IS">Enable 4k video quality will disable Image Stabilization.</string>

    <!-- Toast after 2k4k video is enabled to inform SeeMore has been disabled -->
    <string name="video_quality_4k_disable_SeeMore">SeeMore cannot be enabled during 4k video.</string>

    <!-- Toast after High Quality Noise Reduction has been enabled to show CDS is disabled -->
    <string name="disable_CDS_during_HighQualityNoiseReduction">High Quality Noise Reduction will disable CDS.</string>

    <!-- Toast after High Quality Noise Reduction has been enabled to show TNR is disabled -->
    <string name="disable_TNR_during_HighQualityNoiseReduction">High Quality Noise Reduction will disable TNR.</string>

    <!-- Toast after SeeMore has been enabled to show CDS is disabled -->
    <string name="disable_CDS_during_SeeMore">SeeMore will disable CDS.</string>

    <!-- Toast after SeeMore has been enabled to show TNR is disabled -->
    <string name="disable_TNR_during_SeeMore">SeeMore will disable TNR.</string>

    <!-- Toast after SeeMore has been enabled to show NR is disabled -->
    <string name="disable_NR_during_SeeMore">SeeMore will disable Noise Reduction.</string>

    <!-- Exposure settings in preference -->
    <string name="pref_exposure_title">Exposure</string>
    <string name="pref_exposure_default" translatable="false">0</string>
    <!--  menu label exposure compensation [CHAR LIMIT=50] -->
    <string name="pref_exposure_label">EXPOSURE</string>

    <!-- Default HDR entry value -->
    <string name="pref_camera_hdr_default">@string/setting_off_value</string>

    <!-- Default HDR+ entry value -->
    <string name="pref_camera_hdr_plus_default">@string/setting_off_value</string>

    <!-- HDR label ON [CHAR LIMIT=60] -->
    <string name="pref_camera_hdr_label">HDR</string>

    <!-- switch camera label back [CHAR LIMIT=60] -->
    <string name="pref_camera_id_label_back">FRONT CAMERA</string>
    <!-- switch camera label front [CHAR LIMIT=60] -->
    <string name="pref_camera_id_label_front">BACK CAMERA</string>

    <!-- Dialog "OK" button. Dismisses dialog. -->
    <string name="dialog_ok">OK</string>

    <!-- Low-memory dialog message [CHAR LIMIT=NONE] -->
    <string name="spaceIsLow_content" product="default">Your SD card is running out of space. Change the quality setting or delete some images or other files.</string>

    <!-- Camera format string for new image files. Passed to java.text.SimpleDateFormat. -->
    <string name="image_file_name_format" translatable="false">"'IMG'_yyyyMMdd_HHmmss"</string>

    <!-- Video Camera format string for new video files. Passed to java.text.SimpleDateFormat. -->
    <string name="video_file_name_format" translatable="false">"'VID'_yyyyMMdd_HHmmss"</string>

    <!-- Filename prefix for panorama output. -->
    <string name="pano_file_name_format" translatable="false">"'PANO'_yyyyMMdd_HHmmss"</string>

    <!-- The message shown when video record reaches size limit. -->
    <string name="video_reach_size_limit">Size limit reached.</string>

    <!-- The text shown when the panorama panning speed is to fast [CHAR LIMIT=16] -->
    <string name="pano_too_fast_prompt">Too fast</string>

    <!-- The text shown in the progress dialog when panorama preview is generating in the background [CHAR LIMIT=30] -->
    <string name="pano_dialog_prepare_preview">Preparing panorama</string>

    <!-- The text shown in the dialog when panorama saving failed [CHAR LIMIT=40] -->
    <string name="pano_dialog_panorama_failed">Couldn\'t save panorama.</string>

    <!-- The text shown on the dialog title in the dialogs for Panorama [CHAR LIMIT=12] -->
    <string name="pano_dialog_title">Panorama</string>

    <!-- The text shown on the top-left corner of the screen to indicate the capturing is on going [CHAR LIMIT=27] -->
    <string name="pano_capture_indication">Capturing panorama</string>

    <!-- The text shown in the progress dialog when waiting for previous panorama finishing [CHAR LIMIT=40] -->
    <string name="pano_dialog_waiting_previous">Waiting for previous panorama</string>

    <!-- The text shown on the bottom-left corner of the screen to indicate that the saving is in process [CHAR LIMIT=13] -->
    <string name="pano_review_saving_indication_str">Saving\u2026</string>

    <!-- The text shown on the screen to indicate that the panorama is rendering [CHAR LIMIT=27] -->
    <string name="pano_review_rendering">Rendering panorama</string>

    <!-- Toast telling users tapping on the viewfinder will trigger autofocus [CHAR LIMIT=28] -->
    <string name="tap_to_focus">Touch to focus.</string>

    <!-- Default effect setting that clears the effect. -->
    <string name="pref_video_effect_default" translatable="false">none</string>

    <!-- Title of video effect setting popup window -->
    <string name="pref_video_effect_title">Effects</string>

    <!-- Effect setting item that clear the effect. [CHAR LIMIT=14] -->
    <string name="effect_none">None</string>
    <!-- Effect setting item that squeezes the face. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_squeeze">Squeeze</string>
    <!-- Effect setting item that makes eyes big. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_big_eyes">Big eyes</string>
    <!-- Effect setting item that makes mouth big. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_big_mouth">Big mouth</string>
    <!-- Effect setting item that makes mouth small. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_small_mouth">Small mouth</string>
    <!-- Effect setting item that makes nose big. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_big_nose">Big nose</string>
    <!-- Effect setting item that makes eyes small. [CHAR LIMIT=14] -->
    <string name="effect_goofy_face_small_eyes">Small eyes</string>
    <!-- Effect setting item that replaces background with Android in Space. [CHAR LIMIT=14] -->
    <string name="effect_backdropper_space">In space</string>
    <!-- Effect setting item that replaces background with a sunset. [CHAR LIMIT=14] -->
    <string name="effect_backdropper_sunset">Sunset</string>
    <!-- Effect setting item that replaces background with video from gallery. [CHAR LIMIT=14] -->
    <string name="effect_backdropper_gallery">Your video</string>

    <!-- Toast telling users tapping on the viewfinder will take a picture [CHAR LIMIT=54] -->
    <string name="video_snapshot_hint">Touch to take photo while recording.</string>

    <!-- Announcement telling users video recording has just started [CHAR LIMIT=NONE] -->
    <string name="video_recording_started">Video recording has started.</string>
    <!-- Announcement telling users video recording has just stopped [CHAR LIMIT=NONE] -->
    <string name="video_recording_stopped">Video recording has stopped.</string>

    <!-- A button in effect setting popup to clear the effect. [CHAR LIMIT=26] -->
    <string name="clear_effects">Clear effects</string>

    <!-- Title of category for silly face effects. [CHAR LIMIT=26] -->
    <string name="effect_silly_faces">SILLY FACES</string>

    <!-- Title of category for background replacement effects. [CHAR LIMIT=26] -->
    <string name="effect_background">BACKGROUND</string>

    <!-- The shutter button. [CHAR LIMIT = NONE] -->
    <string name="accessibility_shutter_button">Shutter</string>
    <!-- The exit best photo mode button. [CHAR LIMIT = NONE] -->
    <string name="accessibility_exit_best_mode_button">Exit best photo mode</string>
    <!-- The menu button. [CHAR LIMIT = NONE] -->
    <string name="accessibility_menu_button">Menu button</string>
    <!-- The mode picker to switch between camera, video and panorama. [CHAR LIMIT = NONE] -->
    <string name="accessibility_mode_picker">Camera, video, or panorama selector</string>
    <!-- The check box in camera settings, such as store location. [CHAR LIMIT = NONE] -->
    <string name="accessibility_check_box">%1$s check box</string>
    <!-- The button to switch to Camera mode. [CHAR LIMIT = NONE] -->
    <string name="accessibility_switch_to_camera">Switch to photo</string>
    <!-- The button to switch to Video mode. [CHAR LIMIT = NONE] -->
    <string name="accessibility_switch_to_video">Switch to video</string>
    <!-- The button to switch to Panorama mode. [CHAR LIMIT = NONE] -->
    <string name="accessibility_switch_to_panorama">Switch to panorama</string>
    <!-- The button to switch to the Photo Sphere mode. [CHAR LIMIT = NONE] -->
    <string name="accessibility_switch_to_photo_sphere">Switch to Photo Sphere</string>
    <!-- The button to switch to the high quality mode. [CHAR LIMIT = NONE] -->
    <string name="accessibility_switch_to_gcam">Switch to high quality</string>
    <!-- The button in review mode indicating that the photo taking, video recording, and panorama saving session should be canceled [CHAR LIMIT = NONE] -->
    <string name="accessibility_review_cancel">Review cancel</string>
    <!-- The button in review mode indicating that the taken photo/video is OK to be attached/uploaded [CHAR LIMIT = NONE] -->
    <string name="accessibility_review_ok">Review done</string>
    <!-- button in review mode indicate the user want to retake another photo/video for attachment [
CHAR LIMIT = NONE] -->
    <string name="accessibility_review_retake">Review retake</string>
    <!-- Default text for a button that can be toggled on and off. -->
    <string name="capital_on">ON</string>
    <!-- Default text for a button that can be toggled on and off. -->
    <string name="capital_off">OFF</string>

    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_off">Off</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_500">0.5 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_1000">1 second</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_1500">1.5 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_2000">2 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_2500">2.5 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_3000">3 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_4000">4 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_5000">5 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_6000">6 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_10000">10 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_12000">12 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_15000">15 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_24000">24 seconds</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_30000">0.5 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_60000">1 minute</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_90000">1.5 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_120000">2 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_150000">2.5 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_180000">3 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_240000">4 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_300000">5 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_360000">6 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_600000">10 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_720000">12 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_900000">15 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_1440000">24 minutes</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_1800000">0.5 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_3600000">1 hour</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_5400000">1.5 hour</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_7200000">2 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_9000000">2.5 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_10800000">3 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_14400000">4 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_18000000">5 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_21600000">6 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_36000000">10 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_43200000">12 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_54000000">15 hours</string>
    <!-- Text to indicate time lapse recording frame interval [CHAR LIMIT = 30] -->
    <string name="pref_video_time_lapse_frame_interval_86400000">24 hours</string>

    <!-- Seconds: a unit of time for time lapse intervals. [CHAR LIMIT = 20] -->
    <string name="time_lapse_seconds">seconds</string>
    <!-- Minutes: a unit of time for time lapse intervals. [CHAR LIMIT = 20] -->
    <string name="time_lapse_minutes">minutes</string>
    <!-- Hours: a unit of time for time lapse intervals. [CHAR LIMIT = 20] -->
    <string name="time_lapse_hours">hours</string>

    <!-- The button to confirm time-lapse setting changes. [CHAR LIMIT = 20] -->
    <string name="time_lapse_interval_set">Done</string>
    <!-- Title in time interval picker for setting time interval. [CHAR LIMIT = 30]-->
    <string name="set_time_interval">Set Time Interval</string>
    <!-- Help text that is shown when the time lapse feature is turned off. [CHAR LIMIT = 180]-->
    <string name="set_time_interval_help">Time lapse feature is off. Turn it on to set time interval.</string>
    <!-- Title in timer setting for setting the duration for the countdown timer. [CHAR LIMIT = 50]-->
    <string name="set_duration">Set duration in seconds</string>
    <!-- On-screen hint during timer countdown for taking a photo. [CHAR LIMIT = 60]-->
    <string name="count_down_title_text">Counting down to take a photo</string>

    <!-- Title for first run dialog asking if the user wants to remember photo locations [CHAR LIMIT = 50] -->
    <string name="remember_location_title">Remember photo locations?</string>
    <!-- Message for first run dialog asking if the user wants to remember photo locations [CHAR LIMIT = None] -->
    <string name="remember_location_prompt">Tag your photos and videos with the locations where they are taken.\n\nOther apps can access this information along with your saved images.</string>
    <!-- Negative answer for first run dialog asking if the user wants to remember photo locations [CHAR LIMIT = 20] -->
    <string name="remember_location_no">No thanks</string>
    <!-- Positive answer for first run dialog asking if the user wants to remember photo locations [CHAR LIMIT = 20] -->
    <string name="remember_location_yes">Yes</string>

    <!-- Camera menu labels -->
    <!-- more options label [CHAR LIMIT=50] -->
    <string name="camera_menu_more_label">MORE OPTIONS</string>
    <!-- settings label [CHAR LIMIT=50] -->
    <string name="camera_menu_settings_label">SETTINGS</string>

    <!--  Tiny Planet -->
    <!-- Button to press for creating a tiny planet image. [CHAR LIMIT = 30] -->
    <string name="create_tiny_planet">Create Tiny Planet</string>
    <!-- Message shown while a tiny planet image is being saved. [CHAR LIMIT = 30] -->
    <string name="saving_tiny_planet">Saving Tiny Planet …</string>
    <!-- Label above a slider that let's the user set the zoom of a tiny planet image. [CHAR LIMIT = 15] -->
    <string name="tiny_planet_zoom">Zoom</string>
    <!-- Label above a slider that let's the user set the rotation of a tiny planet image. [CHAR LIMIT = 15] -->
    <string name="tiny_planet_rotate">Rotate</string>

    <!-- Crop -->
    <!-- Label for the save button in the crop activity action bar [CHAR LIMIT=20] -->
    <string name="crop_save">Save</string>
    <!--  String shown when we cannot load the image when starting the activity [CHAR LIMIT=NONE] -->
    <string name="cannot_load_image">Cannot load the image!</string>
    <!-- Label for album filmstrip button -->
    <string name="switch_photo_filmstrip">Filmstrip view</string>

    <!--  String shown when setting the homepage wallpaper in the background [CHAR LIMIT=NONE] -->
    <string name="setting_wallpaper">Setting wallpaper</string>


    <!-- Menu, Video rotation labels [CHAR LIMIT=50] -->
    <string name="pref_camera_video_rotation_label_0">0</string>
    <string name="pref_camera_video_rotation_label_90">90</string>
    <string name="pref_camera_video_rotation_label_180">180</string>
    <string name="pref_camera_video_rotation_label_270">270</string>

    <string name="remaining_photos_format">%d left</string>
    <string name="initial_recording_seconds">00:00</string>
    <string name="makeup_video_size_limit">Makeup works only under VGA size in video recording.</string>

    <string name="pref_camera2_category_system">System</string>
    <string name="pref_camera2_restore_default">Restore defaults</string>
    <string name="pref_camera2_restore_default_hint">Camera settings will be restored to default settings</string>

    <string name="save_best_dialog_content">%1$d of 10 images will be saved and the rest will deleted.</string>
    <string name="save_best_dialog_title">Save select images ?</string>
    <string name="save_best_dialog_positive_bt">OK</string>
    <string name="save_best_dialog_native_bt">Cancel</string>
    <string name="save_best_image_toast">%1$d of 10 images saved.</string>
    <string name="dialog_do_not_show">Do not show again</string>
    <string name="delete_best_dialog_title">Delete All ?</string>
    <string name="delete_best_dialog_content">Are you sure to delete all 10 pictures taken?</string>
    <string name="delete_best_dialog_native_bt">Cancel</string>
    <string name="delete_best_dialog_positive_bt">Delete</string>
    <string name="delete_all_best_dialog_title">Delete All ?</string>
    <string name="delete_all_best_dialog_content">Are you sure to delete all images?</string>
    <string name="delete_all_best_dialog_native_bt">Cancel</string>
    <string name="delete_all_best_dialog_positive_bt">Delete All</string>
    <string name="overflow_best_item1">Save All</string>
    <string name="overflow_best_item2">Delete All</string>

    <string name="settings_title">Settings</string>
    <string name="camera_default_mode">Default</string>
    <string name="camera_bokeh_mode">Bokeh</string>
    <string name="camera_sat_mode">SAT</string>
    <string name="camera_pano_mode">Panorama</string>
    <string name="camera_pro_mode">ProMode</string>
 </resources>
