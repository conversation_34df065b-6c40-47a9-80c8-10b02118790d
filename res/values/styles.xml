<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2011 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Camera resources below -->
    <style name="Theme.ProxyLauncher" parent="@android:Theme.Translucent.NoTitleBar">
    </style>

    <style name="Theme.OneUISettings" parent="@android:style/Theme.Material.Light.NoActionBar.Fullscreen">
        <item name="android:colorAccent">#5999e1</item>
    </style>
    <style name="Theme.Settings" parent="@android:style/Theme.Material.Light">
        <item name="android:colorAccent">#5999e1</item>
    </style>

    <style name="Theme.Camera" parent="Theme.CameraBase">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowTitleSize">0dp</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:colorBackground">@android:color/black</item>
        <item name="android:colorBackgroundCacheHint">@android:color/black</item>
        <item name="android:actionBarStyle">@style/Holo.ActionBar</item>
    </style>
    <style name="Theme.CameraBase" parent="android:Theme.Holo"/>
    <style name="Theme.Crop" parent="Theme.GalleryBase">
        <item name="android:displayOptions"></item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:actionBarStyle">@style/Holo.ActionBar</item>
        <item name="android:colorBackground">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowBackground">@drawable/crop_tiled_background</item>
    </style>
    <style name="Holo.ActionBar" parent="android:Widget.Holo.ActionBar">
        <item name="android:displayOptions">useLogo|showHome|homeAsUp</item>
        <item name="android:background">@drawable/actionbar_translucent</item>
        <item name="android:backgroundStacked">@null</item>
    </style>
    <style name="OnScreenHintTextAppearance">
        <item name="android:textColor">@android:color/primary_text_dark</item>
        <item name="android:textColorHighlight">#FFFF9200</item>
        <item name="android:textColorHint">#808080</item>
        <item name="android:textColorLink">#5C5CFF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
    </style>
    <style name="OnScreenHintTextAppearance.Small">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@android:color/secondary_text_dark</item>
    </style>
    <style name="Animation_OnScreenHint">
        <item name="android:windowEnterAnimation">@anim/on_screen_hint_enter</item>
        <item name="android:windowExitAnimation">@anim/on_screen_hint_exit</item>
    </style>
    <style name="ReviewPlayIcon">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:visibility">gone</item>
        <item name="android:src">@drawable/ic_gallery_play_big</item>
    </style>
    <style name="PopupTitleSeparator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">2dp</item>
        <item name="android:background">@color/popup_title_color</item>
    </style>
    <style name="SettingItemList">
        <item name="android:orientation">vertical</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:listSelector">@drawable/bg_pressed</item>
    </style>
    <style name="CustomSettingItemTitle">
        <item name="android:textSize">14sp</item>
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    <style name="SettingItemTitle">
        <item name="android:textSize">@dimen/setting_item_text_size</item>
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    <style name="SettingItemText">
        <item name="android:layout_width">@dimen/setting_item_text_width</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:textSize">@dimen/setting_item_text_size</item>
    </style>
    <style name="SettingRow">
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/setting_row_height</item>
        <item name="android:paddingLeft">@dimen/setting_item_list_margin</item>
        <item name="android:paddingRight">@dimen/setting_item_list_margin</item>
        <item name="android:background">@drawable/setting_picker</item>
    </style>
    <style name="CustomSettingRow">
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">3000dp</item>
        <item name="android:layout_height">@dimen/setting_row_height</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">@dimen/setting_item_list_margin</item>
        <item name="android:background">@drawable/setting_picker</item>
    </style>
    <style name="OnViewfinderLabel">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginTop">38dp</item>
        <item name="android:paddingLeft">15dp</item>
        <item name="android:paddingRight">15dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16dp</item>
        <item name="android:background">@drawable/bg_text_on_preview</item>
    </style>
    <style name="PanoCustomDialogText">
        <item name="android:textAppearance">@android:style/TextAppearance.Medium</item>
    </style>
    <style name="EffectSettingGrid">
        <item name="android:layout_marginLeft">@dimen/setting_item_list_margin</item>
        <item name="android:layout_marginRight">@dimen/setting_item_list_margin</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:numColumns">3</item>
        <item name="android:verticalSpacing">3dp</item>
        <item name="android:horizontalSpacing">3dp</item>
        <item name="android:choiceMode">singleChoice</item>
    </style>
    <style name="EffectSettingItem">
        <item name="android:orientation">vertical</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">9dp</item>
        <item name="android:paddingBottom">9dp</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:background">@drawable/setting_picker</item>
    </style>
    <style name="EffectSettingItemTitle">
        <item name="android:textSize">@dimen/effect_setting_item_text_size</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingTop">1dp</item>
    </style>
    <style name="EffectSettingTypeTitle">
        <item name="android:textSize">@dimen/effect_setting_type_text_size</item>
        <item name="android:gravity">left|center_vertical</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:alpha">0.7</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">@dimen/effect_setting_type_text_min_height</item>
        <item name="android:paddingLeft">@dimen/effect_setting_type_text_left_padding</item>
    </style>
    <style name="EffectTypeSeparator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_marginLeft">8dp</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:layout_marginBottom">14dp</item>
        <item name="android:layout_height">2dp</item>
        <item name="android:background">#2c2c2c</item>
    </style>
    <style name="EffectTitleSeparator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">2dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:background">@android:drawable/divider_horizontal_dark</item>
    </style>
    <style name="TextAppearance.DialogWindowTitle" parent="">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/holo_blue_light</item>
    </style>
    <style name="TextAppearance.Medium" parent="@android:style/TextAppearance.Medium"/>
    <style name="Widget.Button.Borderless" parent="android:Widget.Button">
        <item name="android:background">@drawable/bg_pressed</item>
        <item name="android:textAppearance">@style/TextAppearance.Medium</item>
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">64dip</item>
        <item name="android:paddingLeft">4dip</item>
        <item name="android:paddingRight">4dip</item>
    </style>

    <style name="ReviewControlText_xlarge">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:background">@drawable/bg_pressed_exit_fading</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:paddingTop">10dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    <style name="PopupTitleText_xlarge">
        <item name="android:textSize">@dimen/popup_title_text_size</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/popup_title_color</item>
        <item name="android:layout_marginLeft">10dp</item>
    </style>
    <style name="PanoCustomDialogText_xlarge">
        <item name="android:textAppearance">@android:style/TextAppearance.Large</item>
    </style>
    <style name="ViewfinderLabelLayout_xlarge">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_margin">13dp</item>
    </style>
    <style name="SwitcherButton">
        <item name="android:layout_width">@dimen/switcher_size</item>
        <item name="android:layout_height">@dimen/switcher_size</item>
        <item name="android:background">@drawable/bg_pressed_exit_fading</item>
    </style>
    <style name="MenuButton">
        <item name="android:layout_width">@dimen/menu_outer_size</item>
        <item name="android:layout_height">@dimen/menu_outer_size</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="MuteButton">
        <item name="android:layout_width">@dimen/mute_outer_size</item>
        <item name="android:layout_height">@dimen/mute_outer_size</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="ToggleButton">
        <item name="android:layout_width">@dimen/toggle_outer_size</item>
        <item name="android:layout_height">@dimen/toggle_outer_size</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="OneUIMenuButton">
        <item name="android:layout_width">25dp</item>
        <item name="android:layout_height">25dp</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="MenuIndicator">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:enabled">false</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="CameraControls">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>
    <style name="UndoBar">
        <item name="android:layout_marginLeft">4dp</item>
        <item name="android:layout_marginRight">4dp</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_gravity">bottom</item>
        <item name="android:background">@drawable/panel_undo_holo</item>
    </style>
    <style name="UndoBarTextAppearance">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>
    <style name="UndoBarSeparator">
        <item name="android:background">@color/gray</item>
        <item name="android:layout_width">1dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="UndoButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/gray</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:background">@drawable/bg_pressed</item>
    </style>
    <style name="PanoViewHorizontalBar">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="OnViewfinderSceneLabel">
        <item name="android:gravity">center</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16dp</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="BestPhoneOverflow">
        <item name="android:textColor">@android:color/black</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">18sp</item>
    </style>
</resources>
