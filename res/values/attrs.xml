<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
    <declare-styleable name="Theme.GalleryBase">
        <attr name="listPreferredItemHeightSmall" format="dimension" />
        <attr name="switchStyle" format="reference" />
    </declare-styleable>

    <!-- Camera resources below -->

    <declare-styleable name="CameraPreference">
        <attr name="title" format="string" />
    </declare-styleable>
    <declare-styleable name="ListPreference">
        <attr name="key" format="string" />
        <attr name="defaultValue" format="string|reference" />
        <attr name="entryValues" format="reference" />
        <attr name="entries" format="reference" />
        <attr name="labelList" format="reference" />
        <attr name="dependencyList" format="reference" />
    </declare-styleable>
    <declare-styleable name="IconIndicator">
        <attr name="icons" format="reference" />
        <attr name="modes" format="reference" />
    </declare-styleable>
    <declare-styleable name="IconListPreference">
        <!-- If a preference does not have individual icons for each entry, it can has a single icon to represent it. -->
        <attr name="singleIcon" format="reference" />
        <attr name="icons" />
        <attr name="largeIcons" format="reference" />
        <attr name="thumbnails" format="reference" />
        <attr name="images" format="reference" />
    </declare-styleable>

    <declare-styleable name="MenuHelp">
        <attr name="forCamera2" format="boolean"/>
    </declare-styleable>
</resources>
