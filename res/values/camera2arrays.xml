<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-2017, The Linux Foundation. All rights reserved.

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are
     met:
      * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
      * Redistributions in binary form must reproduce the above
        copyright notice, this list of conditions and the following
        disclaimer in the documentation and/or other materials provided
        with the distribution.
      * Neither the name of The Linux Foundation nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

     THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
     WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     MERC<PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
     ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
     BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEM<PERSON>AR<PERSON>, OR
     <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
     BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
     OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
     IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<resources>
    <string-array name="pref_camera2_camera2_entries" translatable="true">
        <item>@string/pref_camera2_camera2_entry_disable</item>
        <item>@string/pref_camera2_camera2_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_camera2_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_mono_only_entries" translatable="true">
        <item>On</item>
        <item>Off</item>
    </string-array>

    <string-array name="pref_camera2_mono_only_entryvalues" translatable="false">
        <item>@string/setting_on_value</item>
        <item>@string/setting_off_value</item>
    </string-array>

    <string-array name="pref_camera2_initial_camera_entries" translatable="true">
        <item>Bayer</item>
        <item>Mono</item>
        <item>Front</item>
    </string-array>

    <string-array name="pref_camera2_initial_camera_entryvalues" translatable="false">
        <item>bayer</item>
        <item>mono</item>
        <item>front</item>
    </string-array>

    <string-array name="pref_camera2_makeup_entries" translatable="true">
        <item>0</item>
        <item>10</item>
        <item>20</item>
        <item>30</item>
        <item>40</item>
        <item>50</item>
        <item>60</item>
        <item>70</item>
        <item>80</item>
        <item>90</item>
        <item>100</item>
    </string-array>

    <string-array name="pref_camera2_makeup_entryvalues" translatable="false">
        <item>0</item>
        <item>10</item>
        <item>20</item>
        <item>30</item>
        <item>40</item>
        <item>50</item>
        <item>60</item>
        <item>70</item>
        <item>80</item>
        <item>90</item>
        <item>100</item>
    </string-array>

    <string-array name="pref_camera2_trackingfocus_entries" translatable="true">
        <item>On</item>
        <item>Off</item>
    </string-array>

    <string-array name="pref_camera2_trackingfocus_entryvalues" translatable="false">
        <item>on</item>
        <item>off</item>
    </string-array>

    <string-array name="pref_camera2_mono_preview_entries" translatable="true">
        <item>@string/pref_camera2_mono_preview_entry_on</item>
        <item>@string/pref_camera2_mono_preview_entry_off</item>
    </string-array>

    <string-array name="pref_camera2_mono_preview_entryvalues" translatable="false">
        <item>@string/setting_on_value</item>
        <item>@string/setting_off_value</item>
    </string-array>

    <string-array name="pref_camera2_clearsight_entries" translatable="true">
        <item>@string/pref_camera2_clearsight_entry_off</item>
        <item>@string/pref_camera2_clearsight_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_clearsight_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_mpo_entries" translatable="true">
        <item>@string/pref_camera2_mpo_entry_off</item>
        <item>@string/pref_camera2_mpo_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_mpo_entryvalues" translatable="false">
        <item>@string/pref_camera2_mpo_value_off</item>
        <item>@string/pref_camera2_mpo_value_on</item>
    </string-array>


    <!-- Refer to CONTROL_SCENE_MODE of Camera2 API for values
        -1 refers to ones not supported in Camera2 API
        0 is special case added for auto (meaning off)
        100 is for dual mode (Custom-Scenemodes start from 100)
    -->
    <string-array name="pref_camera2_scenemode_entryvalues" translatable="false">
        <item>0</item>
        <item>100</item>
        <item>18</item>
        <item>102</item>
        <item>101</item>
        <item>3</item>
        <item>4</item>
        <item>13</item>
        <item>-1</item>
        <item>-1</item>
        <item>15</item>
        <item>10</item>
        <item>5</item>
        <item>8</item>
        <item>9</item>
        <item>-1</item>
        <item>103</item>
        <item>105</item>
        <item>106</item>
        <item>107</item>
        <item>108</item>
        <item>104</item>
        <item>109</item>
        <item>110</item>
	<item>111</item>
    </string-array>

    <!-- Camera Preferences Scene Mode dialog box entries -->
    <string-array name="pref_camera2_scenemode_entries" translatable="false">
        <item>@string/pref_camera_scenemode_entry_auto</item>
        <item>Dual</item>
        <item>@string/pref_camera_scenemode_entry_hdr</item>
        <item>@string/pref_camera_scenemode_entry_refocus</item>
        <item>@string/pref_camera_scenemode_entry_optizoom</item>
        <item>@string/pref_camera_scenemode_entry_portrait</item>
        <item>@string/pref_camera_scenemode_entry_landscape</item>
        <item>@string/pref_camera_scenemode_entry_sports</item>
        <item>@string/pref_camera_scenemode_entry_flowers</item>
        <item>@string/pref_camera_scenemode_entry_backlight</item>
        <item>@string/pref_camera_scenemode_entry_candlelight</item>
        <item>@string/pref_camera_scenemode_entry_sunset</item>
        <item>@string/pref_camera_scenemode_entry_night</item>
        <item>@string/pref_camera_scenemode_entry_beach</item>
        <item>@string/pref_camera_scenemode_entry_snow</item>
        <item>@string/pref_camera_scenemode_entry_asd</item>
        <item>@string/pref_camera_scenemode_entry_bestpicture</item>
        <item>@string/pref_camera_scenemode_entry_chromaflash</item>
        <item>@string/pref_camera_scenemode_entry_blurbuster</item>
        <item>@string/pref_camera_scenemode_entry_sharpshooter</item>
        <item>@string/pref_camera_scenemode_entry_trackingfocus</item>
        <item>@string/pref_camera_scenemode_entry_panorama</item>
        <item>@string/pref_camera_scenemode_entry_promode</item>
        <item>@string/pref_camera_scenemode_entry_deepzoom</item>
	<item>@string/pref_camera_scenemode_entry_deepportrait</item>
    </string-array>

    <array name="pref_camera2_scenemode_thumbnails" translatable="false">
        <item>@drawable/auto</item>
        <item>@drawable/hdr</item>
        <item>@drawable/hdr</item>
        <item>@drawable/ubifocus</item>
        <item>@drawable/optizoom</item>
        <item>@drawable/portrait</item>
        <item>@drawable/landscape</item>
        <item>@drawable/sports</item>
        <item>@drawable/flower</item>
        <item>@drawable/backlight</item>
        <item>@drawable/candlelight</item>
        <item>@drawable/sunset</item>
        <item>@drawable/night</item>
        <item>@drawable/beach</item>
        <item>@drawable/snow</item>
        <item>@drawable/ic_scene_mode_smartauto</item>
        <item>@drawable/pick_the_best_photo</item>
        <item>@drawable/chroma_flash</item>
        <item>@drawable/blur_buster</item>
        <item>@drawable/sharp_photo</item>
        <item>@drawable/tracking_focus</item>
        <item>@drawable/scene_panorama</item>
        <item>@drawable/promode</item>
        <item>@drawable/sharp_photo</item>
	<item>@drawable/deep_portrait</item>
    </array>

    <array name="pref_camera2_scenemode_black_thumbnails" translatable="false">
        <item>@drawable/ic_scene_mode_black_auto</item>
        <item>@drawable/ic_scene_mode_black_dual_camera</item>
        <item>@drawable/ic_scene_mode_black_hdr</item>
        <item>@drawable/ic_scene_mode_black_ubifocus</item>
        <item>@drawable/ic_scene_mode_black_optizoom</item>
        <item>@drawable/ic_scene_mode_black_portrait</item>
        <item>@drawable/ic_scene_mode_black_landscape</item>
        <item>@drawable/ic_scene_mode_black_sports</item>
        <item>@drawable/ic_scene_mode_black_flowers</item>
        <item>@drawable/ic_scene_mode_black_backlight</item>
        <item>@drawable/ic_scene_mode_black_candlelight</item>
        <item>@drawable/ic_scene_mode_black_sunset</item>
        <item>@drawable/ic_scene_mode_black_night</item>
        <item>@drawable/ic_scene_mode_black_beach</item>
        <item>@drawable/ic_scene_mode_black_snow</item>
        <item>@drawable/ic_scene_mode_smartauto</item>
        <item>@drawable/ic_scene_mode_black_best_photo</item>
        <item>@drawable/ic_scene_mode_black_chroma_flash</item>
        <item>@drawable/ic_scene_mode_black_blur_buster</item>
        <item>@drawable/ic_scene_mode_black_sharp_photo</item>
        <item>@drawable/ic_scene_mode_black_tracking_focus</item>
        <item>@drawable/ic_scene_mode_black_panorama</item>
        <item>@drawable/ic_scene_mode_black_dual_camera</item>
        <item>@drawable/ic_scene_mode_black_sharp_photo</item>
	<item>@drawable/deep_portrait_black</item>
    </array>

    <!-- Camera Preferences Scene Mode dialog box entries -->
    <string-array name="pref_camera2_scenemode_instructional_entries" translatable="false">
        <item>@string/pref_camera_scenemode_entry_auto</item>
        <item>@string/pref_camera2_scene_mode_dual_camera_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_hdr_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_ubi_focus_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_opti_zoom_instructional_content</item>
        <item>""</item>
        <item>""</item>
        <item>@string/pref_camera2_scene_mode_sports_instructional_content</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>""</item>
        <item>@string/pref_camera2_scene_mode_best_photo_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_chroma_flash_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_blur_buster_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_sharp_photo_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_tracking_focus_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_panorama_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_pro_instructional_content</item>
        <item>@string/pref_camera2_scene_mode_deepzoom_instructional_content</item>
	<item>@string/pref_camera2_scene_mode_deepportrait_instructional_content</item>
    </string-array>

    <string-array name="pref_camera2_whitebalance_entryvalues" translatable="false">
        <item>1</item>
        <item>2</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
    </string-array>

    <!-- Camera Preferences White Balance dialog box entries -->
    <string-array name="pref_camera2_whitebalance_entries" translatable="false">
        <item>@string/pref_camera_whitebalance_entry_auto</item>
        <item>@string/pref_camera_whitebalance_entry_incandescent</item>
        <item>@string/pref_camera_whitebalance_entry_fluorescent</item>
        <item>@string/pref_camera_whitebalance_entry_daylight</item>
        <item>@string/pref_camera_whitebalance_entry_cloudy</item>
    </string-array>

    <string-array name="pref_camera2_whitebalance_labels" translatable="false">
        <item>@string/pref_camera_whitebalance_label_auto</item>
        <item>@string/pref_camera_whitebalance_label_incandescent</item>
        <item>@string/pref_camera_whitebalance_label_fluorescent</item>
        <item>@string/pref_camera_whitebalance_label_daylight</item>
        <item>@string/pref_camera_whitebalance_label_cloudy</item>
    </string-array>

    <array name="pref_camera2_whitebalance_icons" translatable="false">
        <item>@drawable/ic_wb_auto</item>
        <item>@drawable/ic_wb_incandescent</item>
        <item>@drawable/ic_wb_fluorescent</item>
        <item>@drawable/ic_wb_sunlight</item>
        <item>@drawable/ic_wb_cloudy</item>
    </array>

    <array name="pref_camera2_whitebalance_largeicons" translatable="false">
        <item>@drawable/ic_wb_incandescent</item>
        <item>@drawable/ic_wb_fluorescent</item>
        <item>@drawable/ic_wb_auto</item>
        <item>@drawable/ic_wb_sunlight</item>
        <item>@drawable/ic_wb_cloudy</item>
    </array>

    <!-- Refer to CONTROL_EFFECT_MODE of Camera2 API for values
        -1 refers to ones not supported in Camera2 API
    -->
    <string-array name="pref_camera2_coloreffect_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>4</item>
        <item>2</item>
        <item>3</item>
        <item>5</item>
        <item>8</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
        <item>-1</item>
    </string-array>

    <string-array name="pref_camera2_coloreffect_icons" translatable="false">
        <item>@drawable/ic_settings_filter</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
        <item>@drawable/ic_settings_filter_on</item>
    </string-array>

    <!-- Camera Preferences Color effect dialog box entries -->
    <string-array name="pref_camera2_coloreffect_entries" translatable="false">
        <item>@string/pref_camera_coloreffect_entry_none</item>
        <item>@string/pref_camera_coloreffect_entry_mono</item>
        <item>@string/pref_camera_coloreffect_entry_sepia</item>
        <item>@string/pref_camera_coloreffect_entry_negative</item>
        <item>@string/pref_camera_coloreffect_entry_solarize</item>
        <item>@string/pref_camera_coloreffect_entry_posterize</item>
        <item>@string/pref_camera_coloreffect_entry_aqua</item>
        <item>@string/pref_camera_coloreffect_entry_emboss</item>
        <item>@string/pref_camera_coloreffect_entry_sketch</item>
        <item>@string/pref_camera_coloreffect_entry_neon</item>
        <item>@string/pref_camera_coloreffect_entry_pastel</item>
        <item>@string/pref_camera_coloreffect_entry_mosaic</item>
        <item>@string/pref_camera_coloreffect_entry_redtint</item>
        <item>@string/pref_camera_coloreffect_entry_bluetint</item>
        <item>@string/pref_camera_coloreffect_entry_greentint</item>
    </string-array>

    <array name="pref_camera2_coloreffect_thumbnails" translatable="false">
        <item>@drawable/thumb_filter_nofilter</item>
        <item>@drawable/thumb_filter_monochrome</item>
        <item>@drawable/thumb_filter_sepia</item>
        <item>@drawable/thumb_filter_negative</item>
        <item>@drawable/thumb_filter_solarize</item>
        <item>@drawable/thumb_filter_posterize</item>
        <item>@drawable/thumb_filter_aqua</item>
        <item>@drawable/thumb_filter_emboss</item>
        <item>@drawable/thumb_filter_sketch</item>
        <item>@drawable/thumb_filter_neon</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
        <item>0</item>
    </array>

    <!-- Camera Preferences flash mode dialog box entries -->
    <string-array name="pref_camera2_flashmode_entries" translatable="false">
        <item>@string/pref_camera_flashmode_entry_off</item>
        <item>@string/pref_camera_flashmode_entry_auto</item>
        <item>@string/pref_camera_flashmode_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_flashmode_labels" translatable="false">
        <item>@string/pref_camera_flashmode_label_off</item>
        <item>@string/pref_camera_flashmode_label_auto</item>
        <item>@string/pref_camera_flashmode_label_on</item>
    </string-array>

    <string-array name="pref_camera2_flashmode_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/pref_camera2_flashmode_value_auto</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <array name="pref_camera2_flashmode_icons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <array name="pref_camera2_flashmode_largeicons" translatable="false">
        <item>@drawable/ic_flash_off_holo_light</item>
        <item>@drawable/ic_flash_on_holo_light</item>
    </array>

    <!-- Camera Preference save path entries -->
    <string-array name="pref_camera2_savepath_entries" translatable="false">
        <item>@string/pref_camera_savepath_entry_0</item>
        <item>@string/pref_camera_savepath_entry_1</item>
    </string-array>

    <string-array name="pref_camera2_savepath_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <!-- Camera Preferences Picture size dialog box entries -->
    <string-array name="pref_camera2_picturesize_entries" translatable="false">
        <item>@string/pref_camera_picturesize_entry_256mp</item>
        <item>8192 x 8192</item>
        <item>8192 x 4320</item>
        <item>7680 x 4320</item>
        <item>@string/pref_camera_picturesize_entry_48mp</item>
        <item>@string/pref_camera_picturesize_entry_36mp</item>
        <item>@string/pref_camera_picturesize_entry_32mp</item>
        <item>@string/pref_camera_picturesize_entry_28mp</item>
        <item>@string/pref_camera_picturesize_entry_25mp</item>
        <item>@string/pref_camera_picturesize_entry_24mp</item>
        <item>@string/pref_camera_picturesize_entry_21mp</item>
        <item>5184 x 3880</item>
        <item>@string/pref_camera_picturesize_entry_16mp</item>
        <item>@string/pref_camera_picturesize_entry_16mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_13mp</item>
        <item>4208 x 3120</item>
        <item>@string/pref_camera_picturesize_entry_12mp</item>
        <item>@string/pref_camera_picturesize_entry_8mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_8mp</item>
        <item>@string/pref_camera_picturesize_entry_square</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_5mp</item>
        <item>@string/pref_camera_picturesize_entry_4mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1920x1080</item>
        <item>@string/pref_camera_picturesize_entry_2mp</item>
        <item>@string/pref_camera_picturesize_entry_2mp_wide</item>
        <item>@string/pref_camera_picturesize_entry_1_5mp</item>
        <item>@string/pref_camera_picturesize_entry_1_3mp</item>
        <item>@string/pref_camera_picturesize_entry_1280x768</item>
        <item>@string/pref_camera_picturesize_entry_1280x720</item>
        <item>@string/pref_camera_picturesize_entry_1280x400</item>
        <item>@string/pref_camera_picturesize_entry_1mp</item>
        <item>@string/pref_camera_picturesize_entry_800x600</item>
        <item>@string/pref_camera_picturesize_entry_800x480</item>
        <item>@string/pref_camera_picturesize_entry_960x720</item>
        <item>@string/pref_camera_picturesize_entry_720x480</item>
        <item>@string/pref_camera_picturesize_entry_vga</item>
        <item>@string/pref_camera_picturesize_entry_352x288</item>
        <item>@string/pref_camera_picturesize_entry_qvga</item>
    </string-array>
    <!-- When launching the camera app first time, we will set the picture
         size to the first one in the list that is also supported by the
         driver -->
    <string-array name="pref_camera2_picturesize_entryvalues" translatable="false">
        <item>16000x16000</item>
        <item>8192x8192</item>
        <item>8192x4320</item>
        <item>7680x4320</item>
        <item>8000x6000</item>
        <item>6928x5196</item>
        <item>6560x4928</item>
        <item>6112x4584</item>
        <item>5184x4882</item>
        <item>5656x4242</item>
        <item>5344x4008</item>
        <item>5184x3880</item>
        <item>4608x3456</item>
        <item>5312x2988</item>
        <item>4160x3120</item>
        <item>4208x3120</item>
        <item>4000x3000</item>
        <item>3840x2160</item>
        <item>3264x2448</item>
        <item>2976x2976</item>
        <item>2592x1944</item>
        <item>2592x1936</item>
        <item>2560x1920</item>
        <item>2688x1512</item>
        <item>2048x1536</item>
        <item>2048x1520</item>
        <item>1920x1080</item>
        <item>1600x1200</item>
        <item>1920x1088</item>
        <item>1440x1080</item>
        <item>1280x960</item>
        <item>1280x768</item>
        <item>1280x720</item>
        <item>1280x400</item>
        <item>1024x768</item>
        <item>800x600</item>
        <item>800x480</item>
        <item>960x720</item>
        <item>720x480</item>
        <item>640x480</item>
        <item>352x288</item>
        <item>320x240</item>
    </string-array>

    <string-array name="pref_camera2_picture_format_entries" translatable="false">
        <item>@string/pref_camera2_picture_format_entry_0</item>
        <item>@string/pref_camera2_picture_format_entry_1</item>
    </string-array>

    <string-array name="pref_camera2_picture_format_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <!-- Camera Preferences focus mode dialog box entries -->
    <string-array name="pref_camera2_focusmode_entries" translatable="false">
        <item>@string/pref_camera_focusmode_entry_auto</item>
        <item>@string/pref_camera_focusmode_entry_infinity</item>
        <item>@string/pref_camera_focusmode_entry_macro</item>
        <item>@string/pref_camera_focusmode_entry_normal</item>
        <item>@string/pref_camera_focusmode_entry_continuous</item>
    </string-array>

    <string-array name="pref_camera2_focusmode_entryvalues" translatable="false">
        <item>auto</item>
        <item>infinity</item>
        <item>macro</item>
        <item>normal</item>
        <item>continuous-picture</item>
    </string-array>

    <string-array name="pref_camera2_focusmode_labels" translatable="false">
        <item>@string/pref_camera_focusmode_label_auto</item>
        <item>@string/pref_camera_focusmode_label_infinity</item>
        <item>@string/pref_camera_focusmode_label_macro</item>
    </string-array>

    <string-array name="pref_camera2_recordlocation_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <array name="pref_camera2_recordlocation_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </array>

    <array name="pref_camera2_recordlocation_labels" translatable="false">
        <item>@string/pref_camera_location_label</item>
        <item>@string/pref_camera_location_label</item>
    </array>

    <array name="pref_camera2_recordlocation_icons" translatable="false">
        <item>@drawable/ic_location_off</item>
        <item>@drawable/ic_location</item>
    </array>

    <array name="pref_camera2_recordlocation_largeicons" translatable="false">
        <item>@drawable/ic_location_off</item>
        <item>@drawable/ic_location</item>
    </array>

    <array name="pref_camera2_id_entries" translatable="false">
        <item>@string/pref_camera_id_entry_back</item>
        <item>@string/pref_camera_id_entry_front</item>
    </array>

    <array name="pref_camera2_id_labels" translatable="false">
        <item>@string/pref_camera_id_label_back</item>
        <item>@string/pref_camera_id_label_front</item>
    </array>

    <array name="pref_camera2_id_icons" translatable="false">
        <item>@drawable/ic_switch_back</item>
        <item>@drawable/ic_switch_front</item>
    </array>

    <array name="pref_camera2_id_largeicons" translatable="false">
        <item>@drawable/ic_switch_back</item>
        <item>@drawable/ic_switch_front</item>
    </array>

    <string-array name="pref_camera2_timer_sound_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera2_timer_sound_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <!-- Icons for exposure compensation -->
    <array name="pref_camera2_exposure_icons" translatable="false">
        <item>@drawable/ic_exposure_n3</item>
        <item>@drawable/ic_exposure_n2</item>
        <item>@drawable/ic_exposure_n1</item>
        <item>@drawable/ic_exposure_0</item>
        <item>@drawable/ic_exposure_p1</item>
        <item>@drawable/ic_exposure_p2</item>
        <item>@drawable/ic_exposure_p3</item>
    </array>

    <!--  Labels for Countdown timer -->
    <string-array name="pref_camera2_countdown_labels">
        <item>@string/pref_camera_countdown_label_off</item>
        <item>@string/pref_camera_countdown_label_one</item>
        <item>@string/pref_camera_countdown_label_three</item>
        <item>@string/pref_camera_countdown_label_ten</item>
        <item>@string/pref_camera_countdown_label_fifteen</item>
    </string-array>

    <!-- Camera Preferences JPEG quality dialog box entries -->
    <string-array name="pref_camera2_jpegquality_entries" translatable="false">
        <item>@string/pref_camera_jpegquality_entry_0</item>
        <item>@string/pref_camera_jpegquality_entry_1</item>
        <item>@string/pref_camera_jpegquality_entry_2</item>
    </string-array>

    <string-array name="pref_camera2_jpegquality_entryvalues" translatable="false">
        <item>55</item>
        <item>85</item>
        <item>100</item>
    </string-array>

    <!-- Rough estimates of jpeg compression ratio corresponding to qualities defined above. -->
    <integer-array name="pref_camera2_jpegquality_compression_ratio">
        <item>48</item>
        <item>20</item>
        <item>6</item>
    </integer-array>

    <!-- Camera Preferences ISO dialog box entries -->
    <string-array name="pref_camera2_iso_entries">
        <item>@string/pref_camera_iso_entry_auto</item>
        <item>@string/pref_camera_iso_entry_iso100</item>
        <item>@string/pref_camera_iso_entry_iso200</item>
        <item>@string/pref_camera_iso_entry_iso400</item>
        <item>@string/pref_camera_iso_entry_iso800</item>
        <item>@string/pref_camera_iso_entry_iso1600</item>
        <item>@string/pref_camera_iso_entry_iso3200</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera2_iso_entryvalues">
        <item>auto</item>
        <item>100</item>
        <item>200</item>
        <item>400</item>
        <item>800</item>
        <item>1600</item>
        <item>3200</item>
    </string-array>

    <!-- Camera Preferences Auto Exposure dialog box entries -->
    <string-array name="pref_camera2_autoexposure_entries">
        <item>@string/pref_camera_autoexposure_entry_frameaverage</item>
        <item>@string/pref_camera_autoexposure_entry_centerweighted</item>
        <item>@string/pref_camera_autoexposure_entry_spotmetering</item>
    </string-array>

    <!-- Do not localize entryvalues -->
    <string-array name="pref_camera2_autoexposure_entryvalues">
        <item>@string/pref_camera_autoexposure_value_frameaverage</item>
        <item>@string/pref_camera_autoexposure_value_centerweighted</item>
        <item>@string/pref_camera_autoexposure_value_spotmetering</item>
    </string-array>

    <!-- Camera Preferences Redeye Reduction dialog box entries -->
    <string-array name="pref_camera2_redeyereduction_entries" translatable="false">
        <item>@string/pref_camera_redeyereduction_entry_disable</item>
        <item>@string/pref_camera_redeyereduction_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_redeyereduction_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <!-- Camera Preferences Long Shot dialog box entries -->
    <string-array name="pref_camera2_longshot_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera2_longshot_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_selfiemirror_entries" translatable="false">
        <item>@string/setting_off</item>
        <item>@string/setting_on</item>
    </string-array>

    <string-array name="pref_camera2_selfiemirror_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_filter_mode_entries" translatable="false">
        <item>@string/pref_camera_filter_mode_entry_off</item>
        <item>@string/pref_camera_filter_mode_entry_on</item>
    </string-array>
    <string-array name="pref_camera2_filter_mode_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_filter_mode_icons" translatable="false">
        <item>@drawable/ic_settings_filter</item>
        <item>@drawable/ic_settings_filter_on</item>
    </string-array>

    <string-array name="pref_camera2_video_quality_entries" translatable="false">
        <item>@string/pref_video_quality_entry_8k</item>
        <item>@string/pref_video_quality_entry_2160p</item>
        <item>@string/pref_video_quality_entry_qHD</item>
        <item>@string/pref_video_quality_entry_2k</item>
        <item>@string/pref_video_quality_entry_1080p</item>
        <item>@string/pref_video_quality_entry_720p</item>
        <item>@string/pref_video_quality_entry_480p</item>
        <item>@string/pref_video_quality_entry_vga</item>
        <item>@string/pref_video_quality_entry_cif</item>
        <item>@string/pref_video_quality_entry_qvga</item>
    </string-array>

    <string-array name="pref_camera2_video_quality_entryvalues" translatable="false">
        <item>7680x4320</item>
        <item>3840x2160</item>
        <item>2560x1440</item>
        <item>2048x1080</item>
        <item>1920x1080</item>
        <item>1280x720</item>
        <item>720x480</item>
        <item>640x480</item>
        <item>352x288</item>
        <item>320x240</item>
    </string-array>

    <string-array name="pref_camera2_video_duration_entries" translatable="false">
        <item>@string/pref_camera_video_duration_entry_mms</item>
        <item>@string/pref_camera_video_duration_entry_10</item>
        <item>@string/pref_camera_video_duration_entry_30</item>
        <item>@string/pref_camera_video_duration_entry_48</item>
        <item>@string/pref_camera_video_duration_entry_144</item>
        <item>@string/pref_camera_video_duration_entry_nolimit</item>
    </string-array>

    <!-- The numbers are in minutes, except -1 means the duration suitable for mms. -->
    <string-array name="pref_camera2_video_duration_entryvalues" translatable="false">
        <item>-1</item>
        <item>10</item>
        <item>30</item>
        <item>48</item>
        <item>144</item>
        <item>0</item>
    </string-array>

    <string-array name="pref_camera2_videoencoder_entries" translatable="false">
        <item>@string/pref_camera_videoencoder_entry_0</item>
        <item>@string/pref_camera_videoencoder_entry_1</item>
        <item>@string/pref_camera_videoencoder_entry_2</item>
        <item>@string/pref_camera_videoencoder_entry_3</item>
    </string-array>

    <string-array name="pref_camera2_videoencoder_entryvalues" translatable="false">
        <item>mpeg-4-sp</item>
        <item>h263</item>
        <item>h264</item>
        <item>h265</item>
    </string-array>

    <string-array name="pref_camera2_audioencoder_entries" translatable="false">
        <item>@string/pref_camera_audioencoder_entry_0</item>
        <item>@string/pref_camera_audioencoder_entry_1</item>
        <item>@string/pref_camera_audioencoder_entry_2</item>
    </string-array>

    <string-array name="pref_camera2_audioencoder_entryvalues" translatable="false">
        <item>amr-nb</item>
        <item>aac</item>
        <item>off</item>
    </string-array>

    <string-array name="pref_camera2_dis_entries" translatable="false">
        <item>@string/pref_camera_dis_entry_off</item>
        <item>@string/pref_camera_dis_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_dis_entryvalues"  translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_qcfa_entries" translatable="false">
        <item>@string/pref_camera_qcfa_value_disable</item>
        <item>@string/pref_camera_qcfa_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_qcfa_entryvalues"  translatable="false">
        <item>@string/pref_camera_qcfa_value_disable</item>
        <item>@string/pref_camera_qcfa_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_noise_reduction_entries" translatable="false">
        <item>@string/pref_camera2_noise_reduction_entry_off</item>
        <item>@string/pref_camera2_noise_reduction_entry_fast</item>
        <item>@string/pref_camera2_noise_reduction_entry_high_quality</item>
    </string-array>

    <string-array name="pref_camera2_noise_reduction_entryvalues" translatable="false">
        <item>@string/pref_camera2_noise_reduction_value_off</item>
        <item>@string/pref_camera2_noise_reduction_value_fast</item>
        <item>@string/pref_camera2_noise_reduction_value_high_quality</item>
    </string-array>

    <string-array name="pref_camera2_video_flashmode_entries" translatable="false">
        <item>@string/pref_camera_flashmode_entry_off</item>
        <item>@string/pref_camera_flashmode_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_video_flashmode_labels" translatable="false">
        <item>@string/pref_camera_flashmode_label_off</item>
        <item>@string/pref_camera_flashmode_label_on</item>
    </string-array>

    <string-array name="pref_camera2_video_flashmode_entryvalues" translatable="false">
        <item>off</item>
        <item>on</item>
    </string-array>

    <string-array name="pref_camera2_video_rotation_entries" translatable="false">
        <item>@string/pref_camera_video_rotation_entry_0</item>
        <item>@string/pref_camera_video_rotation_entry_90</item>
        <item>@string/pref_camera_video_rotation_entry_180</item>
        <item>@string/pref_camera_video_rotation_entry_270</item>
    </string-array>

    <string-array name="pref_camera2_video_rotation_labels" translatable="false">
        <item>@string/pref_camera_video_rotation_label_0</item>
        <item>@string/pref_camera_video_rotation_label_90</item>
        <item>@string/pref_camera_video_rotation_label_180</item>
        <item>@string/pref_camera_video_rotation_label_270</item>
    </string-array>

    <string-array name="pref_camera2_video_rotation_entryvalues" translatable="false">
        <item>0</item>
        <item>90</item>
        <item>180</item>
        <item>270</item>
    </string-array>

    <!-- These values correspond to the time interval between frame capture in millseconds
for time lapse recording -->
    <string-array name="pref_camera2_video_time_lapse_frame_interval_entryvalues" translatable="false">
        <item>0</item>
        <item>500</item>
        <item>1000</item>
        <item>1500</item>
        <item>2000</item>
        <item>2500</item>
        <item>3000</item>
        <item>4000</item>
        <item>5000</item>
        <item>6000</item>
        <item>10000</item>
        <item>12000</item>
        <item>15000</item>
        <item>24000</item>
        <item>30000</item>
        <item>60000</item>
        <item>90000</item>
        <item>120000</item>
        <item>150000</item>
        <item>180000</item>
        <item>240000</item>
        <item>300000</item>
        <item>360000</item>
        <item>600000</item>
        <item>720000</item>
        <item>900000</item>
        <item>1440000</item>
        <item>1800000</item>
        <item>3600000</item>
        <item>5400000</item>
        <item>7200000</item>
        <item>9000000</item>
        <item>10800000</item>
        <item>14400000</item>
        <item>18000000</item>
        <item>21600000</item>
        <item>36000000</item>
        <item>43200000</item>
        <item>54000000</item>
        <item>86400000</item>
    </string-array>

    <!-- These values correspond to the time interval between frame capture in
    different units (i.e. seconds, minutes, hours) for time lapse recording -->
    <string-array name="pref_camera2_video_time_lapse_frame_interval_entries" translatable="true">
        <item>@string/pref_video_time_lapse_frame_interval_off</item>
        <item>@string/pref_video_time_lapse_frame_interval_500</item>
        <item>@string/pref_video_time_lapse_frame_interval_1000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1500</item>
        <item>@string/pref_video_time_lapse_frame_interval_2000</item>
        <item>@string/pref_video_time_lapse_frame_interval_2500</item>
        <item>@string/pref_video_time_lapse_frame_interval_3000</item>
        <item>@string/pref_video_time_lapse_frame_interval_4000</item>
        <item>@string/pref_video_time_lapse_frame_interval_5000</item>
        <item>@string/pref_video_time_lapse_frame_interval_6000</item>
        <item>@string/pref_video_time_lapse_frame_interval_10000</item>
        <item>@string/pref_video_time_lapse_frame_interval_12000</item>
        <item>@string/pref_video_time_lapse_frame_interval_15000</item>
        <item>@string/pref_video_time_lapse_frame_interval_24000</item>
        <item>@string/pref_video_time_lapse_frame_interval_30000</item>
        <item>@string/pref_video_time_lapse_frame_interval_60000</item>
        <item>@string/pref_video_time_lapse_frame_interval_90000</item>
        <item>@string/pref_video_time_lapse_frame_interval_120000</item>
        <item>@string/pref_video_time_lapse_frame_interval_150000</item>
        <item>@string/pref_video_time_lapse_frame_interval_180000</item>
        <item>@string/pref_video_time_lapse_frame_interval_240000</item>
        <item>@string/pref_video_time_lapse_frame_interval_300000</item>
        <item>@string/pref_video_time_lapse_frame_interval_360000</item>
        <item>@string/pref_video_time_lapse_frame_interval_600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_720000</item>
        <item>@string/pref_video_time_lapse_frame_interval_900000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1440000</item>
        <item>@string/pref_video_time_lapse_frame_interval_1800000</item>
        <item>@string/pref_video_time_lapse_frame_interval_3600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_5400000</item>
        <item>@string/pref_video_time_lapse_frame_interval_7200000</item>
        <item>@string/pref_video_time_lapse_frame_interval_9000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_10800000</item>
        <item>@string/pref_video_time_lapse_frame_interval_14400000</item>
        <item>@string/pref_video_time_lapse_frame_interval_18000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_21600000</item>
        <item>@string/pref_video_time_lapse_frame_interval_36000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_43200000</item>
        <item>@string/pref_video_time_lapse_frame_interval_54000000</item>
        <item>@string/pref_video_time_lapse_frame_interval_86400000</item>
    </string-array>

    <string-array name="pref_camera2_facedetection_entries" translatable="false">
        <item>@string/pref_camera_facedetection_entry_off</item>
        <item>@string/pref_camera_facedetection_entry_on</item>
    </string-array>

    <string-array name="pref_camera2_facedetection_entryvalues" translatable="false">
        <item>@string/setting_off_value</item>
        <item>@string/setting_on_value</item>
    </string-array>

    <string-array name="pref_camera2_videosnap_entries" translatable="true">
        <item>@string/pref_camera2_videosnap_entry_enable</item>
        <item>@string/pref_camera2_videosnap_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_videosnap_entryvalues" translatable="false">
        <item>@string/setting_on_value</item>
        <item>@string/setting_off_value</item>
    </string-array>

    <string-array name="pref_camera2_timer_entries" translatable="false">
        <item>@string/pref_camera2_timer_entry_off</item>
        <item>@string/pref_camera2_timer_entry_2sec</item>
        <item>@string/pref_camera2_timer_entry_5sec</item>
        <item>@string/pref_camera2_timer_entry_10sec</item>
    </string-array>

    <string-array name="pref_camera2_timer_entryvalues" translatable="false">
        <item>@string/pref_camera2_timer_value_off</item>
        <item>@string/pref_camera2_timer_value_2sec</item>
        <item>@string/pref_camera2_timer_value_5sec</item>
        <item>@string/pref_camera2_timer_value_10sec</item>
    </string-array>

    <string-array name="pref_camera2_shutter_sound_entries" translatable="true">
        <item>@string/pref_camera2_shutter_sound_entry_on</item>
        <item>@string/pref_camera2_shutter_sound_entry_off</item>
    </string-array>

    <string-array name="pref_camera2_shutter_sound_entryvalues" translatable="false">
        <item>@string/pref_camera2_shutter_sound_value_on</item>
        <item>@string/pref_camera2_shutter_sound_value_off</item>
    </string-array>

    <string-array name="pref_camera2_touch_track_focus_entries" translatable="true">
        <item>@string/pref_camera2_touch_track_focus_entry_on</item>
        <item>@string/pref_camera2_touch_track_focus_off</item>
    </string-array>

    <string-array name="pref_camera2_touch_track_focus_entryvalues" translatable="false">
        <item>@string/pref_camera2_touch_track_focus_value_on</item>
        <item>@string/pref_camera2_touch_track_focus_value_off</item>
    </string-array>

    <string-array name="pref_camera2_instant_aec_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <string-array name="pref_camera2_instant_aec_entries" translatable="true">
        <item>@string/pref_camera2_instant_aec_entry_disable</item>
        <item>@string/pref_camera2_instant_aec_entry_aggressive</item>
        <item>@string/pref_camera2_instant_aec_entry_fast</item>
    </string-array>

    <string-array name="pref_camera2_anti_banding_level_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>

    <string-array name="pref_camera2_anti_banding_level_entries" translatable="true">
        <item>@string/pref_camera2_anti_banding_level_entry_off</item>
        <item>@string/pref_camera2_anti_banding_level_entry_50hz</item>
        <item>@string/pref_camera2_anti_banding_level_entry_60hz</item>
        <item>@string/pref_camera2_anti_banding_level_entry_auto</item>
    </string-array>

    <string-array name="pref_camera2_saturation_level_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
    </string-array>

    <string-array name="pref_camera2_saturation_level_entries" translatable="true">
        <item>@string/pref_camera2_saturation_level_entry_0</item>
        <item>@string/pref_camera2_saturation_level_entry_1</item>
        <item>@string/pref_camera2_saturation_level_entry_2</item>
        <item>@string/pref_camera2_saturation_level_entry_3</item>
        <item>@string/pref_camera2_saturation_level_entry_4</item>
        <item>@string/pref_camera2_saturation_level_entry_5</item>
        <item>@string/pref_camera2_saturation_level_entry_6</item>
        <item>@string/pref_camera2_saturation_level_entry_7</item>
        <item>@string/pref_camera2_saturation_level_entry_8</item>
        <item>@string/pref_camera2_saturation_level_entry_9</item>
        <item>@string/pref_camera2_saturation_level_entry_10</item>
    </string-array>

    <string-array name="pref_camera2_hdr_entryvalues" translatable="false">
        <item>enable</item>
        <item>disable</item>
    </string-array>

    <string-array name="pref_camera2_hdr_entries" translatable="true">
        <item>@string/pref_camera2_hdr_entry_enable</item>
        <item>@string/pref_camera2_hdr_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_auto_hdr_entryvalues" translatable="false">
        <item>enable</item>
        <item>disable</item>
    </string-array>

    <string-array name="pref_camera2_auto_hdr_entries" translatable="true">
        <item>@string/pref_camera2_auto_hdr_entry_enable</item>
        <item>@string/pref_camera2_auto_hdr_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_saveraw_entries" translatable="false">
        <item>@string/pref_camera2_saveraw_entry_disable</item>
        <item>@string/pref_camera2_saveraw_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_saveraw_entryvalues" translatable="false">
        <item>@string/pref_camera2_saveraw_value_disable</item>
        <item>@string/pref_camera2_saveraw_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_bsgc_entryvalues" translatable="false">
        <item>@string/pref_camera2_bsgc_entry_value_disable</item>
        <item>@string/pref_camera2_bsgc_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_bsgc_entries" translatable="false">
        <item>@string/pref_camera2_bsgc_entry_disable</item>
        <item>@string/pref_camera2_bsgc_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_facial_contour_entries" translatable="false">
        <item>@string/pref_camera2_bsgc_entry_disable</item>
        <item>V0</item>
        <item>V1</item>
    </string-array>

    <string-array name="pref_camera2_facial_contour_entryvalues" translatable="false">
        <item>@string/pref_camera2_bsgc_entry_value_disable</item>
        <item>0</item>
        <item>1</item>
    </string-array>



    <string-array name="pref_camera2_zomm_switch_entries" translatable="false">
        <item>1x</item>
        <item>2x</item>
        <item>3x</item>
    </string-array>

    <string-array name="pref_camera2_zomm_switch_entryvalues" translatable="false">
        <item>1.0</item>
        <item>2.0</item>
        <item>3.0</item>
    </string-array>

    <string-array name="pref_camera2_zomm_switch_wide_entries" translatable="false">
        <item>0.65x</item>
        <item>1x</item>
        <item>2x</item>
    </string-array>

    <string-array name="pref_camera2_zomm_switch_wide_entryvalues" translatable="false">
        <item>0.65</item>
        <item>1.0</item>
        <item>2.0</item>
    </string-array>

    <string-array name="pref_camera2_face_detection_entryvalues" translatable="false">
        <item>@string/pref_camera2_face_detection_entry_value_simple</item>
        <item>@string/pref_camera2_face_detection_entry_value_full</item>
    </string-array>

    <string-array name="pref_camera2_face_detection_entries" translatable="false">
        <item>@string/pref_camera2_face_detection_entry_simple</item>
        <item>@string/pref_camera2_face_detection_entry_full</item>
    </string-array>

    <string-array name="pref_camera2_zsl_entries" translatable="false">
        <item>@string/pref_camera2_zsl_entry_disable</item>
        <item>@string/pref_camera2_zsl_entry_app_zsl</item>
        <item>@string/pref_camera2_zsl_entry_hal_zsl</item>
    </string-array>

    <string-array name="pref_camera2_zsl_entryvalues" translatable="false">
        <item>@string/pref_camera2_zsl_entryvalue_disable</item>
        <item>@string/pref_camera2_zsl_entryvalue_app_zsl</item>
        <item>@string/pref_camera2_zsl_entryvalue_hal_zsl</item>
    </string-array>

    <string-array name="pref_camera2_videoencoderprofile_entry" translatable="false">
        <item>Off</item>
        <item>HLG</item>
        <item>HDR10</item>
    </string-array>

    <string-array name="pref_camera2_videoencoderprofile_entryvalues" translatable="false">
        <item>"off"</item>
        <item>HEVCProfileMain10</item>
        <item>HEVCProfileMain10HDR10</item>
    </string-array>

    <string-array name="pref_camera2_sharpness_control_entries">
        <item>@string/pref_camera2_sharpness_control_entry_level0</item>
        <item>@string/pref_camera2_sharpness_control_entry_level1</item>
        <item>@string/pref_camera2_sharpness_control_entry_level2</item>
        <item>@string/pref_camera2_sharpness_control_entry_level3</item>
        <item>@string/pref_camera2_sharpness_control_entry_level4</item>
        <item>@string/pref_camera2_sharpness_control_entry_level5</item>
        <item>@string/pref_camera2_sharpness_control_entry_level6</item>
    </string-array>

    <!-- Entry Valur array for sharpness -->
    <string-array name="pref_camera2_multilevel_sharpness_entryvalues">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
    </string-array>

    <string-array name="pref_camera2_afmode_entries">
        <item>@string/pref_camera2_afmode_entry_disable</item>
        <item>@string/pref_camera2_afmode_entry_off</item>
        <item>@string/pref_camera2_afmode_entry_auto</item>
        <item>@string/pref_camera2_afmode_entry_macro</item>
        <item>@string/pref_camera2_afmode_entry_continuous_video</item>
        <item>@string/pref_camera2_afmode_entry_continuous_picture</item>
        <item>@string/pref_camera2_afmode_entry_edof</item>
    </string-array>

    <string-array name="pref_camera2_afmode_entryvalues">
        <item>@string/pref_camera2_afmode_value_disable</item>
        <item>@string/pref_camera2_afmode_value_off</item>
        <item>@string/pref_camera2_afmode_value_auto</item>
        <item>@string/pref_camera2_afmode_value_macro</item>
        <item>@string/pref_camera2_afmode_value_continuous_video</item>
        <item>@string/pref_camera2_afmode_value_continuous_picture</item>
        <item>@string/pref_camera2_afmode_value_edof</item>
    </string-array>

    <string-array name="pref_camera2_exposure_metering_entries" translatable="false">
        <item>@string/pref_camera2_exposure_metering_entry_frame_average</item>
        <item>@string/pref_camera2_exposure_metering_entry_center_weighted</item>
        <item>@string/pref_camera2_exposure_metering_entry_spot_meteting</item>
    </string-array>

    <string-array name="pref_camera2_exposure_metering_entryvalues" translatable="false">
        <item>@string/pref_camera2_exposure_metering_entryvalue_frame_average</item>
        <item>@string/pref_camera2_exposure_metering_entryvalue_center_weighted</item>
        <item>@string/pref_camera2_exposure_metering_entryvalue_spot_meteting</item>
    </string-array>

    <string-array name="pref_camera2_eis_entries" translatable="false">
        <item>@string/pref_camera2_eis_entry_disable</item>
        <item>@string/pref_camera2_eis_entry_v2enable</item>
        <item>@string/pref_camera2_eis_entry_v3enable</item>
        <item>@string/pref_camera2_eis_entry_v3_set_when_pause</item>
    </string-array>

    <string-array name="pref_camera2_eis_entryvalues" translatable="false">
        <item>@string/pref_camera2_eis_entry_value_disable</item>
        <item>@string/pref_camera2_eis_entry_value_v2enable</item>
        <item>@string/pref_camera2_eis_entry_value_v3enable</item>
        <item>@string/pref_camera2_eis_entry_value_v3_set_when_pause</item>
    </string-array>

    <string-array name="pref_camera2_fovc_entries" translatable="false">
        <item>@string/pref_camera2_fovc_entry_disable</item>
        <item>@string/pref_camera2_fovc_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_fovc_entryvalues" translatable="false">
        <item>@string/pref_camera2_fovc_entry_value_disable</item>
        <item>@string/pref_camera2_fovc_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_video_hdr_entries" translatable="false">
        <item>@string/pref_camera2_video_hdr_entry_disable</item>
        <item>@string/pref_camera2_video_hdr_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_video_hdr_entryvalues" translatable="false">
        <item>@string/pref_camera2_video_hdr_entry_value_disable</item>
        <item>@string/pref_camera2_video_hdr_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_deepportrait_entryvalues" translatable="false">
        <item>@string/pref_camera2_deepportrait_entry_value_disable</item>
        <item>@string/pref_camera2_deepportrait_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_switcher_entryvalues" translatable="false">
        <item>@string/pref_camera2_switcher_entry_value_rear</item>
        <item>@string/pref_camera2_switcher_entry_value_front</item>
    </string-array>

    <string-array name="pref_camera2_switcher_entries" translatable="false">
        <item>@string/pref_camera2_switcher_entry_rear</item>
        <item>@string/pref_camera2_switcher_entry_front</item>
    </string-array>

    <string-array name="pref_camera2_multi_camera_mode_entries" translatable="false">
        <item>disable</item>
        <item>enable</item>
    </string-array>

    <string-array name="pref_camera2_multi_camera_mode_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>

    <string-array name="pref_camera2_physical_camera_entries" translatable="false">
        <item>@string/pref_camera2_physical_camera_entries_defualt</item>
    </string-array>

    <string-array name="pref_camera2_physical_camera_entryvalues" translatable="false">
        <item>@string/pref_camera2_physical_camera_entry_value_defualt</item>
    </string-array>

    <string-array name="pref_camera2_single_physical_camera_entries" translatable="false">
        <item>@string/pref_camera2_physical_camera_entries_defualt</item>
    </string-array>

    <string-array name="pref_camera2_single_physical_camera_entryvalues" translatable="false">
        <item>@string/pref_camera2_physical_camera_entry_value_defualt</item>
    </string-array>

    <string-array name="pref_camera2_force_aux_entryvalues" translatable="false">
        <item>@string/pref_camera2_force_aux_entry_on</item>
        <item>@string/pref_camera2_force_aux_entry_value_off</item>
    </string-array>

    <string-array name="pref_camera2_physical_camera_default" translatable="false">
    </string-array>

    <string-array name="pref_camera2_force_aux_entries" translatable="false">
        <item>@string/pref_camera2_force_aux_entry_on</item>
        <item>@string/pref_camera2_force_aux_entry_off</item>
    </string-array>

    <string-array name="pref_camera2_oncapturebufferlost_entryvalues" translatable="false">
        <item>@string/pref_camera2_oncapturebufferlost_entry_on</item>
        <item>@string/pref_camera2_oncapturebufferlost_entry_value_off</item>
    </string-array>

    <string-array name="pref_camera2_oncapturebufferlost_entries" translatable="false">
        <item>@string/pref_camera2_oncapturebufferlost_entry_on</item>
        <item>@string/pref_camera2_oncapturebufferlost_entry_off</item>
    </string-array>

    <string-array name="pref_camera2_stats_visualizer_enable_entries" translatable="false">
        <item>@string/pref_camera2_stats_visualizer_entry_enable</item>
        <item>@string/pref_camera2_stats_visualizer_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_stats_visualizer_enable_entryvalues" translatable="false">
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable</item>
        <item>@string/pref_camera2_stats_visualizer_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera2_stats_visualizer_entries" translatable="false">
        <item>@string/pref_camera2_stats_visualizer_entry_bg_stats</item>
        <item>@string/pref_camera2_stats_visualizer_entry_be_stats</item>
        <item>@string/pref_camera2_stats_visualizer_entry_hist_stats</item>
        <item>@string/pref_camera2_stats_visualizer_entry_awb_info</item>
        <item>@string/pref_camera2_stats_visualizer_entry_aec_info</item>
    </string-array>

    <string-array name="pref_camera2_stats_visualizer_entryvalues" translatable="false">
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable_bg</item>
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable_be</item>
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable_hist</item>
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable_awb</item>
        <item>@string/pref_camera2_stats_visualizer_entry_value_enable_aec</item>
    </string-array>

    <string-array name="pref_camera2_variable_fps_entries" translatable="false">
        <item>@string/pref_camera2_variable_fps_entry_disable</item>
        <item>@string/pref_camera2_variable_fps_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_variable_fps_entryvalues" translatable="false">
        <item>@string/pref_camera2_variable_fps_entry_value_disable</item>
        <item>@string/pref_camera2_variable_fps_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_video_flip_entries" translatable="false">
        <item>@string/pref_camera2_video_flip_entry_disable</item>
        <item>@string/pref_camera2_video_flip_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_video_flip_entryvalues" translatable="false">
        <item>@string/pref_camera2_video_flip_entry_value_disable</item>
        <item>@string/pref_camera2_video_flip_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_capture_mfnr_entries" translatable="false">
        <item>@string/pref_camera2_capture_mfnr_entry_disable</item>
        <item>@string/pref_camera2_capture_mfnr_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_capture_mfnr_entryvalues" translatable="false">
        <item>@string/pref_camera2_capture_mfnr_entry_value_disable</item>
        <item>@string/pref_camera2_capture_mfnr_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_fs2_entries" translatable="false">
        <item>@string/pref_camera2_fs2_entry_disable</item>
        <item>@string/pref_camera2_fs2_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_fs2_entryvalues" translatable="false">
        <item>@string/pref_camera2_fs2_entry_value_disable</item>
        <item>@string/pref_camera2_fs2_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_burst_limit_entries" translatable="false">
        <item>@string/pref_camera2_burst_limit_entry_disable</item>
        <item>@string/pref_camera2_burst_limit_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_burst_limit_entryvalues" translatable="false">
        <item>@string/pref_camera2_burst_limit_entry_value_disable</item>
        <item>@string/pref_camera2_burst_limit_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_abort_captures_entries" translatable="false">
        <item>@string/pref_camera2_abort_captures_entry_disable</item>
        <item>@string/pref_camera2_abort_captures_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_abort_captures_entryvalues" translatable="false">
        <item>@string/pref_camera2_abort_captures_entry_value_disable</item>
        <item>@string/pref_camera2_abort_captures_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_select_mode_entries" translatable="false">
        <item>@string/pref_camera2_select_mode_entry_default</item>
        <item>@string/pref_camera2_select_mode_entry_sat</item>
        <item>@string/pref_camera2_select_mode_entry_rtb</item>
        <item>@string/pref_camera2_select_mode_entry_single_rear</item>
    </string-array>

    <string-array name="pref_camera2_select_mode_entryvalues" translatable="false">
        <item>@string/pref_camera2_select_mode_entry_value_default</item>
        <item>@string/pref_camera2_select_mode_entry_value_sat</item>
        <item>@string/pref_camera2_select_mode_entry_value_rtb</item>
        <item>@string/pref_camera2_select_mode_entry_value_single_rear</item>
    </string-array>

    <string-array name="pref_camera2_mfhdr_entries" translatable="false">
        <item>@string/pref_camera2_mfhdr_entry_off</item>
        <item>@string/pref_camera2_mfhdr_entry_shdr</item>
        <item>@string/pref_camera2_mfhdr_entry_mfhdr</item>
    </string-array>

    <string-array name="pref_camera2_mfhdr_entryvalues" translatable="false">
        <item>@string/pref_camera2_mfhdr_entry_value_off</item>
        <item>@string/pref_camera2_mfhdr_entry_value_shdr</item>
        <item>@string/pref_camera2_mfhdr_entry_value_mfhdr</item>
    </string-array>

    <string-array name="pref_camera2_gc_shdr_entries" translatable="false">
        <item>@string/pref_camera2_gc_shdr_entry_on</item>
        <item>@string/pref_camera2_gc_shdr_entry_off</item>
    </string-array>

    <string-array name="pref_camera2_gc_shdr_entryvalues" translatable="false">
        <item>@string/pref_camera2_gc_shdr_entry_value_on</item>
        <item>@string/pref_camera2_gc_shdr_entry_value_off</item>
    </string-array>

    <string-array name="pref_camera2_shading_correction_entries" translatable="false">
        <item>@string/pref_camera2_shading_correction_entry_enable</item>
        <item>@string/pref_camera2_shading_correction_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_shading_correction_entryvalues" translatable="false">
        <item>@string/pref_camera2_shading_correction_entry_value_enable</item>
        <item>@string/pref_camera2_shading_correction_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera2_extended_max_zoom_entries" translatable="false">
        <item>@string/pref_camera2_extended_max_zoom_entry_disable</item>
        <item>@string/pref_camera2_extended_max_zoom_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_extended_max_zoom_entryvalues" translatable="false">
        <item>@string/pref_camera2_extended_max_zoom_entry_value_disable</item>
        <item>@string/pref_camera2_extended_max_zoom_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_hvx_shdr_entries" translatable="false">
        <item>@string/pref_camera2_hvx_shdr_entry_enable</item>
        <item>@string/pref_camera2_hvx_shdr_entry_disable</item>
        <item>@string/pref_camera2_hvx_shdr_entry_preview</item>
        <item>@string/pref_camera2_hvx_shdr_entry_recording</item>
    </string-array>

    <string-array name="pref_camera2_hvx_shdr_entry_values" translatable="false">
        <item>@string/pref_camera2_hvx_shdr_entry_value_enable</item>
        <item>@string/pref_camera2_hvx_shdr_entry_value_disable</item>
        <item>@string/pref_camera2_hvx_shdr_entry_value_preview</item>
        <item>@string/pref_camera2_hvx_shdr_entry_value_recording</item>
    </string-array>

    <string-array name="pref_camera2_hvx_mfhdr_entries" translatable="false">
        <item>@string/pref_camera2_hvx_mfhdr_entry_enable</item>
        <item>@string/pref_camera2_hvx_mfhdr_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_hvx_mfhdr_entry_values" translatable="false">
        <item>@string/pref_camera2_hvx_mfhdr_entry_value_enable</item>
        <item>@string/pref_camera2_hvx_mfhdr_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera2_swpdpc_entries" translatable="false">
        <item>@string/pref_camera2_swpdpc_entry_enable</item>
        <item>@string/pref_camera2_swpdpc_entry_disable</item>
    </string-array>

    <string-array name="pref_camera2_swpdpc_entryvalues" translatable="false">
        <item>@string/pref_camera2_swpdpc_entry_value_enable</item>
        <item>@string/pref_camera2_swpdpc_entry_value_disable</item>
    </string-array>

    <string-array name="pref_camera2_master_cb_entries" translatable="false">
        <item>@string/pref_camera2_master_cb_entry_disable</item>
        <item>@string/pref_camera2_master_cb_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_master_cb_entryvalues" translatable="false">
        <item>@string/pref_camera2_master_cb_entry_value_disable</item>
        <item>@string/pref_camera2_master_cb_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_raw_cb_info_entries" translatable="false">
        <item>@string/pref_camera2_raw_cb_info_entry_default</item>
        <item>@string/pref_camera2_raw_cb_info_entry_primary</item>
        <item>@string/pref_camera2_raw_cb_info_entry_master</item>
        <item>@string/pref_camera2_raw_cb_info_entry_all_active</item>
        <item>@string/pref_camera2_raw_cb_info_entry_logical_master</item>
    </string-array>

    <string-array name="pref_camera2_raw_cb_info_entryvalues" translatable="false">
        <item>@string/pref_camera2_raw_cb_info_entry_value_default</item>
        <item>@string/pref_camera2_raw_cb_info_entry_value_primary</item>
        <item>@string/pref_camera2_raw_cb_info_entry_value_master</item>
        <item>@string/pref_camera2_raw_cb_info_entry_value_all_active</item>
        <item>@string/pref_camera2_raw_cb_info_entry_value_logical_master</item>
    </string-array>

    <string-array name="pref_camera2_statsnn_control_entries" translatable="false">
        <item>@string/pref_camera2_statsnn_control_entry_disable</item>
        <item>@string/pref_camera2_statsnn_control_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_statsnn_control_entryvalues" translatable="false">
        <item>@string/pref_camera2_statsnn_control_entry_value_disable</item>
        <item>@string/pref_camera2_statsnn_control_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_qll_entries" translatable="false">
        <item>@string/pref_camera2_qll_entry_disable</item>
        <item>@string/pref_camera2_qll_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_qll_entryvalues" translatable="false">
        <item>@string/pref_camera2_qll_entry_value_disable</item>
        <item>@string/pref_camera2_qll_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_raw_reprocess_entries" translatable="false">
        <item>@string/pref_camera2_raw_reprocess_entry_off</item>
        <item>@string/pref_camera2_raw_reprocess_entry_yuv</item>
        <item>@string/pref_camera2_raw_reprocess_entry_jpeg</item>
        <item>@string/pref_camera2_raw_reprocess_entry_heic</item>
        <item>@string/pref_camera2_raw_reprocess_entry_all_yuv</item>
        <item>@string/pref_camera2_raw_reprocess_entry_all_jpeg</item>
    </string-array>

    <string-array name="pref_camera2_raw_reprocess_entryvalues" translatable="false">
        <item>@string/pref_camera2_raw_reprocess_entry_value_off</item>
        <item>@string/pref_camera2_raw_reprocess_entry_value_yuv</item>
        <item>@string/pref_camera2_raw_reprocess_entry_value_jpeg</item>
        <item>@string/pref_camera2_raw_reprocess_entry_value_heic</item>
        <item>@string/pref_camera2_raw_reprocess_entry_value_all_yuv</item>
        <item>@string/pref_camera2_raw_reprocess_entry_value_all_jpeg</item>
    </string-array>

    <string-array name="pref_camera2_raw_format_entries" translatable="false">
        <item>@string/pref_camera2_raw_format_entry_10</item>
        <item>@string/pref_camera2_raw_format_entry_16</item>
    </string-array>

    <string-array name="pref_camera2_raw_format_entryvalues" translatable="false">
        <item>@string/pref_camera2_raw_format_entry_value_10</item>
        <item>@string/pref_camera2_raw_format_entry_value_16</item>
    </string-array>

    <string-array name="pref_camera2_rawinfo_type_entries" translatable="false">
        <item>@string/pref_camera2_rawinfo_type_entry_mipi</item>
        <item>@string/pref_camera2_rawinfo_type_entry_ife_ideal</item>
        <item>@string/pref_camera2_rawinfo_type_entry_bps_ideal</item>
    </string-array>

    <string-array name="pref_camera2_rawinfo_type_entryvalues" translatable="false">
        <item>@string/pref_camera2_rawinfo_type_entry_value_mipi</item>
        <item>@string/pref_camera2_rawinfo_type_entry_value_ife_ideal</item>
        <item>@string/pref_camera2_rawinfo_type_entry_value_bps_ideal</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_entries" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_entry_disable</item>
        <item>@string/pref_camera2_ai_denoiser_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_entryvalues" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_entry_value_disable</item>
        <item>@string/pref_camera2_ai_denoiser_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_format_entries" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_format_entry_nv12</item>
        <item>@string/pref_camera2_ai_denoiser_format_entry_nv21</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_format_entryvalues" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_format_entry_value_nv12</item>
        <item>@string/pref_camera2_ai_denoiser_format_entry_value_nv21</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_mode_entries" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_mode_entry_aide1</item>
        <item>@string/pref_camera2_ai_denoiser_mode_entry_aide2</item>
        <item>@string/pref_camera2_ai_denoiser_mode_entry_aide_sat</item>
    </string-array>

    <string-array name="pref_camera2_ai_denoiser_mode_entryvalues" translatable="false">
        <item>@string/pref_camera2_ai_denoiser_mode_entry_value_aide1</item>
        <item>@string/pref_camera2_ai_denoiser_mode_entry_value_aide2</item>
        <item>@string/pref_camera2_ai_denoiser_mode_entry_value_aide_sat</item>
    </string-array>

    <string-array name="pref_camera2_insensor_zoom_entries" translatable="false">
        <item>@string/pref_camera2_insensor_zoom_entry_disable</item>
        <item>@string/pref_camera2_insensor_zoom_entry_enable</item>
    </string-array>

    <string-array name="pref_camera2_insensor_zoom_entryvalues" translatable="false">
        <item>@string/pref_camera2_insensor_zoom_entry_value_disable</item>
        <item>@string/pref_camera2_insensor_zoom_entry_value_enable</item>
    </string-array>

    <string-array name="pref_camera2_3A_debug_info_entries" translatable="false">
        <item>disable</item>
        <item>enable</item>
    </string-array>

    <string-array name="pref_camera2_3A_debug_info_entryvalues" translatable="false">
        <item>0</item>
        <item>1</item>
    </string-array>
</resources>
