<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-2017, The Linux Foundation. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are
 met:
     * Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above
       copyright notice, this list of conditions and the following
       disclaimer in the documentation and/or other materials provided
       with the distribution.
     * Neither the name of The Linux Foundation nor the names of its
       contributors may be used to endorse or promote products derived
       from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
 WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 MERC<PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
 ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
 BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEM<PERSON>AR<PERSON>, OR
 <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
 BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
 IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    android:key="screen">
    <PreferenceCategory
        android:key="general"
        android:layout="@layout/preferences_category"
        android:title="@string/perf_camera2_preferences_category_general">
        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/gps_location"
            android:key="pref_camera2_recordlocation_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_recordlocation_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/face_detection"
            android:key="pref_camera2_facedetection_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_facedetection_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_savepath_default"
            android:entries="@array/pref_camera2_savepath_entries"
            android:entryValues="@array/pref_camera2_savepath_entryvalues"
            android:icon="@drawable/storage"
            android:key="pref_camera2_savepath_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_savepath_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_picture_format_default"
            android:entries="@array/pref_camera2_picture_format_entries"
            android:entryValues="@array/pref_camera2_picture_format_entryvalues"
            android:icon="@drawable/ic_settings_storage"
            android:key="pref_camera2_picture_format_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_picture_format_title"/>
    </PreferenceCategory>

    <PreferenceCategory
        android:key="photo"
        android:layout="@layout/preferences_category"
        android:title="@string/perf_camera2_preferences_category_photo">
        <ListPreference
            android:defaultValue="@string/pref_camera2_timer_value_off"
            android:entries="@array/pref_camera2_timer_entries"
            android:entryValues="@array/pref_camera2_timer_entryvalues"
            android:icon="@drawable/countdown_timer"
            android:key="pref_camera2_timer_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_timer_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/continuous_shot"
            android:key="pref_camera2_longshot_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_longshot_title" />
        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_settings_focus"
            android:key="pref_camera2_gridline_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_gridline_title" />
        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_settings_selfiemirror"
            android:key="pref_camera2_selfiemirror_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_selfiemirror_title" />
        <ListPreference
            android:entries="@array/pref_camera2_picturesize_entries"
            android:entryValues="@array/pref_camera2_picturesize_entryvalues"
            android:icon="@drawable/picture_size"
            android:key="pref_camera2_picturesize_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_picturesize_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_jpegquality_default"
            android:entries="@array/pref_camera2_jpegquality_entries"
            android:entryValues="@array/pref_camera2_jpegquality_entryvalues"
            android:icon="@drawable/picture_quality"
            android:key="pref_camera2_jpegquality_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_jpegquality_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_settings_redeye"
            android:key="pref_camera2_redeyereduction_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_redeyereduction_title" />

        <ListPreference
            android:defaultValue="@string/pref_exposure_default"
            android:icon="@drawable/exposure"
            android:key="pref_camera2_exposure_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_exposure_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_whitebalance_default"
            android:entries="@array/pref_camera2_whitebalance_entries"
            android:entryValues="@array/pref_camera2_whitebalance_entryvalues"
            android:icon="@drawable/white_balance"
            android:key="pref_camera2_whitebalance_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_whitebalance_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/selfie_flash"
            android:key="pref_selfie_flash_key"
            android:layout="@layout/preference"
            android:title="@string/pref_selfie_flash_title" />

        <SwitchPreference
            android:defaultValue="true"
            android:icon="@drawable/shutter_sound"
            android:key="pref_camera2_shutter_sound_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_shutter_sound_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/shutter_sound"
            android:key="pref_camera2_touch_track_focus_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_touch_track_focus_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="pref_camera2_mono_only_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_mono_only_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="pref_camera2_mono_preview_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_mono_preview_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="pref_camera2_clearsight_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_clearsight_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="pref_camera2_mpo_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_mpo_title" />
    </PreferenceCategory>

    <PreferenceCategory
        android:key="video"
        android:layout="@layout/preferences_category"
        android:title="@string/perf_camera2_preferences_category_video">
        <ListPreference
            android:entries="@array/pref_camera2_video_quality_entries"
            android:entryValues="@array/pref_camera2_video_quality_entryvalues"
            android:icon="@drawable/video_quality"
            android:key="pref_camera2_video_quality_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_video_quality_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_video_duration_default"
            android:entries="@array/pref_camera2_video_duration_entries"
            android:entryValues="@array/pref_camera2_video_duration_entryvalues"
            android:icon="@drawable/video_duration"
            android:key="pref_camera2_video_duration_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_video_duration_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/image_stabilization"
            android:key="pref_camera2_dis_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera_dis_title" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/shutter_sound"
            android:key="pref_camera2_touch_track_focus_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_touch_track_focus_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_noise_reduction_default"
            android:entries="@array/pref_camera2_noise_reduction_entries"
            android:entryValues="@array/pref_camera2_noise_reduction_entryvalues"
            android:icon="@drawable/noise_reduction"
            android:key="pref_camera2_noise_reduction_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_noise_reduction_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_videoencoder_default"
            android:entries="@array/pref_camera2_videoencoder_entries"
            android:entryValues="@array/pref_camera2_videoencoder_entryvalues"
            android:icon="@drawable/video_encoding"
            android:key="pref_camera2_videoencoder_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_videoencoder_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_videoencoderprofile_default"
            android:entries="@array/pref_camera2_videoencoderprofile_entry"
            android:entryValues="@array/pref_camera2_videoencoderprofile_entryvalues"
            android:icon="@drawable/video_encoding"
            android:key="pref_camera2_videoencoderprofile_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_videoencoderprofile_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_audioencoder_default"
            android:entries="@array/pref_camera2_audioencoder_entries"
            android:entryValues="@array/pref_camera2_audioencoder_entryvalues"
            android:icon="@drawable/audio_encoding"
            android:key="pref_camera2_audioencoder_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_audioencoder_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera_video_rotation_default"
            android:entries="@array/pref_camera2_video_rotation_entries"
            android:entryValues="@array/pref_camera2_video_rotation_entryvalues"
            android:icon="@drawable/video_rotation"
            android:key="pref_camera2_video_rotation_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_video_rotation_title" />

        <ListPreference
            android:defaultValue="@string/pref_video_time_lapse_frame_interval_default"
            android:entries="@array/pref_camera2_video_time_lapse_frame_interval_entries"
            android:entryValues="@array/pref_camera2_video_time_lapse_frame_interval_entryvalues"
            android:icon="@drawable/clock"
            android:key="pref_camera2_video_time_lapse_frame_interval_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_video_time_lapse_frame_interval_title" />

        <ListPreference
            android:entries="@array/pref_camera_hfr_entries"
            android:entryValues="@array/pref_camera_hfr_entryvalues"
            android:icon="@drawable/high_video_frame_rate"
            android:key="pref_camera2_hfr_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_hfr_title" />
    </PreferenceCategory>

    <PreferenceCategory
        android:layout="@layout/preferences_category"
        android:title="@string/pref_camera2_category_system"
        android:textAllCaps="false">
        <Preference
            android:defaultValue="false"
            android:icon="@drawable/restore_defaults"
            android:key="pref_camera2_restore_default_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_restore_default" />
        <Preference
            android:defaultValue="false"
            android:icon="@drawable/version_info"
            android:key="version_info"
            android:layout="@layout/preference"
            android:summary="Version"
            android:title="@string/perf_camera2_version_info" />
    </PreferenceCategory>

    <PreferenceCategory
        android:key="developer"
        android:layout="@layout/preferences_category"
        android:title="DEVELOPER OPTIONS">

        <ListPreference
            android:defaultValue="@string/pref_camera2_instant_aec_default"
            android:entries="@array/pref_camera2_instant_aec_entries"
            android:entryValues="@array/pref_camera2_instant_aec_entryvalues"
            android:key="pref_camera2_instant_aec_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_instant_aec_title" />
        <ListPreference
            android:defaultValue="@string/pref_camera2_saturation_level_default"
            android:entries="@array/pref_camera2_saturation_level_entries"
            android:entryValues="@array/pref_camera2_saturation_level_entryvalues"
            android:key="pref_camera2_saturation_level_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_saturation_level_title" />
        <ListPreference
            android:defaultValue="@string/pref_camera2_anti_banding_level_default"
            android:entries="@array/pref_camera2_anti_banding_level_entries"
            android:entryValues="@array/pref_camera2_anti_banding_level_entryvalues"
            android:key="pref_camera2_anti_banding_level_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_anti_banding_level_title" />
        <ListPreference
            android:defaultValue="@string/pref_camera2_hdr_default"
            android:entries="@array/pref_camera2_hdr_entries"
            android:entryValues="@array/pref_camera2_hdr_entryvalues"
            android:key="pref_camera2_hdr_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_hdr_title" />
        <ListPreference
            android:defaultValue="@string/pref_camera2_auto_hdr_default"
            android:entries="@array/pref_camera2_auto_hdr_entries"
            android:entryValues="@array/pref_camera2_auto_hdr_entryvalues"
            android:key="pref_camera2_auto_hdr_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_auto_hdr_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_saveraw_default"
            android:entries="@array/pref_camera2_saveraw_entries"
            android:entryValues="@array/pref_camera2_saveraw_entryvalues"
            android:key="pref_camera2_saveraw_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_saveraw_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_zsl_default"
            android:entries="@array/pref_camera2_zsl_entries"
            android:entryValues="@array/pref_camera2_zsl_entryvalues"
            android:key="pref_camera2_zsl_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_zsl_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_multi_camera_mode_default"
            android:key="pref_camera2_multi_camera_mode_key"
            android:layout="@layout/preference"
            android:summary="Enable multi camera mode"
            android:title="@string/pref_camera2_multi_camera_mode_title"
            android:entries="@array/pref_camera2_multi_camera_mode_entries"
            android:entryValues="@array/pref_camera2_multi_camera_mode_entryvalues"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_single_physical_camera_default"
            android:key="pref_camera2_single_physical_camera_key"
            android:layout="@layout/preference"
            android:summary="Enable single physical camera"
            android:title="@string/pref_camera2_single_physical_camera_title"
            android:entries="@array/pref_camera2_single_physical_camera_entries"
            android:entryValues="@array/pref_camera2_single_physical_camera_entryvalues"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_camera_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera for preview"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_camera_title"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_camcorder_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera for recording"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_recording_title"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_jpeg_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera to capture JPEG"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_jpeg_title"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_yuv_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera to enable YUV callback"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_yuv_title"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_raw_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera to enable RAW callback"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_raw_title"/>


        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_hdr_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera to enable HDR"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_hdr_title"/>

        <MultiSelectListPreference
            android:defaultValue="@array/pref_camera2_physical_camera_default"
            android:key="pref_camera2_physical_mfnr_key"
            android:layout="@layout/preference"
            android:summary="Choose which camera to enable MFNR"
            android:entries="@array/pref_camera2_physical_camera_entries"
            android:entryValues="@array/pref_camera2_physical_camera_entryvalues"
            android:title="@string/pref_camera2_physical_mfnr_title"/>

        <ListPreference
            android:entries="@array/pref_camera2_picturesize_entries"
            android:entryValues="@array/pref_camera2_picturesize_entryvalues"
            android:key="pref_camera2_physical_size_0_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_size_title"/>
        <ListPreference
            android:entries="@array/pref_camera2_picturesize_entries"
            android:entryValues="@array/pref_camera2_picturesize_entryvalues"
            android:key="pref_camera2_physical_size_1_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_size_title"/>
        <ListPreference
            android:entries="@array/pref_camera2_picturesize_entries"
            android:entryValues="@array/pref_camera2_picturesize_entryvalues"
            android:key="pref_camera2_physical_size_2_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_size_title"/>
        <ListPreference
            android:defaultValue="@string/pref_camera2_video_quality_default"
            android:entries="@array/pref_camera2_video_quality_entries"
            android:entryValues="@array/pref_camera2_video_quality_entryvalues"
            android:key="pref_camera2_physical_quality_0_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_quality_title"/>
        <ListPreference
            android:defaultValue="@string/pref_camera2_video_quality_default"
            android:entries="@array/pref_camera2_video_quality_entries"
            android:entryValues="@array/pref_camera2_video_quality_entryvalues"
            android:key="pref_camera2_physical_quality_1_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_quality_title"/>
        <ListPreference
            android:defaultValue="@string/pref_camera2_video_quality_default"
            android:entries="@array/pref_camera2_video_quality_entries"
            android:entryValues="@array/pref_camera2_video_quality_entryvalues"
            android:key="pref_camera2_physical_quality_2_key"
            android:layout="@layout/preference"
            android:title="@string/pref_camera2_physical_quality_title"/>
        <ListPreference
            android:key="pref_camera2_zoom_key"
            android:defaultValue="@string/pref_camera_zoom_default"
            android:title="@string/pref_camera_zoom_title"
            android:summary="%s"
            android:entries="@array/pref_camera_zoom_entries"
            android:entryValues="@array/pref_camera_zoom_entryvalues" />

        <ListPreference
            android:key="pref_camera2_manual_exp_key"
            android:defaultValue="@string/pref_camera_manual_exp_default"
            android:title="@string/pref_camera_manual_exp_title"
            android:layout="@layout/preference"
            android:summary="%s"
            android:entries="@array/pref_camera_manual_exp_entries"
            android:entryValues="@array/pref_camera_manual_exp_entry_values" />

        <ListPreference
            android:key="pref_camera2_tone_mapping_key"
            android:defaultValue="@string/pref_camera_tone_mapping_default"
            android:title="@string/pref_camera_tone_mapping_title"
            android:layout="@layout/preference"
            android:summary="%s"
            android:entries="@array/pref_camera_tone_mapping_entries"
            android:entryValues="@array/pref_camera_tone_mapping_entry_values" />

        <ListPreference
            android:defaultValue="@string/pref_camera_manual_wb_default"
            android:entries="@array/pref_camera_manual_wb_entries"
            android:entryValues="@array/pref_camera_manual_wb_entry_values"
            android:key="pref_camera2_manual_wb_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera_manual_wb_title" />

        <ListPreference
            android:key="pref_camera2_qcfa_key"
            android:defaultValue="@string/pref_camera2_qcfa_default"
            android:title="@string/pref_camera2_qcfa_title"
            android:layout="@layout/preference"
            android:summary="%s"
            android:entries="@array/pref_camera2_qcfa_entries"
            android:entryValues="@array/pref_camera2_qcfa_entryvalues" />

        <ListPreference
            android:key="pref_camera2_fd_smile_key"
            android:defaultValue="@string/pref_camera2_bsgc_default"
            android:entries="@array/pref_camera2_bsgc_entries"
            android:entryValues="@array/pref_camera2_bsgc_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_fd_smile_title" />

        <ListPreference
            android:key="pref_camera2_fd_gaze_key"
            android:defaultValue="@string/pref_camera2_bsgc_default"
            android:entries="@array/pref_camera2_bsgc_entries"
            android:entryValues="@array/pref_camera2_bsgc_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_fd_gaze_title" />

        <ListPreference
            android:key="pref_camera2_fd_blink_key"
            android:defaultValue="@string/pref_camera2_bsgc_default"
            android:entries="@array/pref_camera2_bsgc_entries"
            android:entryValues="@array/pref_camera2_bsgc_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_fd_blink_title" />


        <ListPreference
            android:key="pref_camera2_facial_contour_key"
            android:defaultValue="@string/pref_camera2_bsgc_default"
            android:title="@string/pref_camera2_facial_contour_title"
            android:layout="@layout/preference"
            android:summary="%s"
            android:entries="@array/pref_camera2_facial_contour_entries"
            android:entryValues="@array/pref_camera2_facial_contour_entryvalues" />

        <ListPreference
            android:key="pref_camera2_face_detection_mode"
            android:defaultValue="@string/pref_camera2_face_points_default"
            android:title="@string/pref_camera2_face_detection_mode_title"
            android:layout="@layout/preference"
            android:summary="%s"
            android:entries="@array/pref_camera2_face_detection_entries"
            android:entryValues="@array/pref_camera2_face_detection_entryvalues" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_sharpness_control_default"
            android:entries="@array/pref_camera2_sharpness_control_entries"
            android:entryValues="@array/pref_camera2_multilevel_sharpness_entryvalues"
            android:key="pref_camera2_sharpness_control_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_sharpness_control_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_afmode_default"
            android:entries="@array/pref_camera2_afmode_entries"
            android:entryValues="@array/pref_camera2_afmode_entryvalues"
            android:key="pref_camera2_afmode_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_afmode_title" />

        <ListPreference
            android:defaultValue="@string/pref_camera2_exposure_metering_default"
            android:entries="@array/pref_camera2_exposure_metering_entries"
            android:entryValues="@array/pref_camera2_exposure_metering_entryvalues"
            android:key="pref_camera2_exposure_metering_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_exposure_metering_title" />

        <ListPreference
            android:key="pref_camera2_eis_key"
            android:defaultValue="@string/pref_camera2_eis_default"
            android:title="@string/pref_camera2_eis_title"
            android:entries="@array/pref_camera2_eis_entries"
            android:entryValues="@array/pref_camera2_eis_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_fovc_key"
            android:defaultValue="@string/pref_camera2_fovc_default"
            android:title="@string/pref_camera2_fovc_title"
            android:entries="@array/pref_camera2_fovc_entries"
            android:entryValues="@array/pref_camera2_fovc_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_video_hdr_key"
            android:defaultValue="@string/pref_camera2_video_hdr_default"
            android:title="@string/pref_camera2_video_hdr_title"
            android:entries="@array/pref_camera2_video_hdr_entries"
            android:entryValues="@array/pref_camera2_video_hdr_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_force_aux_key"
            android:defaultValue="@string/pref_camera2_force_aux_default"
            android:title="@string/pref_camera2_force_aux_title"
            android:entries="@array/pref_camera2_force_aux_entries"
            android:entryValues="@array/pref_camera2_force_aux_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_hvx_shdr_key"
            android:defaultValue="@string/pref_camera2_hvx_shdr_default"
            android:title="@string/pref_camera2_hvx_shdr_title"
            android:entries="@array/pref_camera2_hvx_shdr_entries"
            android:entryValues="@array/pref_camera2_hvx_shdr_entry_values"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_hvx_mfhdr_key"
            android:defaultValue="@string/pref_camera2_hvx_mfhdr_default"
            android:title="@string/pref_camera2_hvx_mfhdr_title"
            android:entries="@array/pref_camera2_hvx_mfhdr_entries"
            android:entryValues="@array/pref_camera2_hvx_mfhdr_entry_values"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_oncapturebufferlost_key"
            android:defaultValue="@string/pref_camera2_oncapturebufferlost_default"
            android:title="@string/pref_camera2_oncapturebufferlost_title"
            android:entries="@array/pref_camera2_oncapturebufferlost_entries"
            android:entryValues="@array/pref_camera2_oncapturebufferlost_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_switch_camera_default"
            android:key="pref_camera2_switch_camera_key"
            android:layout="@layout/preference"
            android:summary="%s"
            android:title="@string/pref_camera2_switch_camera_title"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_select_mode_default"
            android:key="pref_camera2_select_mode_key"
            android:title="@string/pref_camera2_select_mode_title"
            android:entries="@array/pref_camera2_select_mode_entries"
            android:entryValues="@array/pref_camera2_select_mode_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_raw_reprocess_default"
            android:key="pref_camera2_raw_reprocess_key"
            android:title="@string/pref_camera2_raw_reprocess_title"
            android:entries="@array/pref_camera2_raw_reprocess_entries"
            android:entryValues="@array/pref_camera2_raw_reprocess_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_raw_format_default"
            android:key="pref_camera2_raw_format_key"
            android:title="@string/pref_camera2_raw_format_title"
            android:entries="@array/pref_camera2_raw_format_entries"
            android:entryValues="@array/pref_camera2_raw_format_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:defaultValue="@string/pref_camera2_rawinfo_type_default"
            android:key="pref_camera2_rawinfo_type_key"
            android:title="@string/pref_camera2_rawinfo_type_title"
            android:entries="@array/pref_camera2_rawinfo_type_entries"
            android:entryValues="@array/pref_camera2_rawinfo_type_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <MultiSelectListPreference
            android:key="pref_camera2_stats_visualizer_key"
            android:defaultValue="@array/pref_camera2_stats_visualizer_default"
            android:title="@string/pref_camera2_stats_visualizer_title"
            android:entries="@array/pref_camera2_stats_visualizer_entries"
            android:entryValues="@array/pref_camera2_stats_visualizer_entryvalues"
            android:layout="@layout/preference"
            android:summary="Choose stats to display"/>

        <ListPreference
            android:key="pref_camera2_variable_fps_key"
            android:defaultValue="@string/pref_camera2_variable_fps_default"
            android:title="@string/pref_camera2_variable_fps_title"
            android:entries="@array/pref_camera2_variable_fps_entries"
            android:entryValues="@array/pref_camera2_variable_fps_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_video_flip_key"
            android:defaultValue="@string/pref_camera2_video_flip_default"
            android:title="@string/pref_camera2_video_flip_title"
            android:entries="@array/pref_camera2_video_flip_entries"
            android:entryValues="@array/pref_camera2_video_flip_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_capture_mfnr_key"
            android:defaultValue="@string/pref_camera2_capture_mfnr_default"
            android:title="@string/pref_camera2_capture_mfnr_title"
            android:entries="@array/pref_camera2_capture_mfnr_entries"
            android:entryValues="@array/pref_camera2_capture_mfnr_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_live_preview_key"
            android:defaultValue="@string/pref_camera2_live_preview_default"
            android:title="@string/pref_camera2_live_preview_title"
            android:entries="@array/pref_camera2_live_preview_entries"
            android:entryValues="@array/pref_camera2_live_preview_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_fs2_key"
            android:defaultValue="@string/pref_camera2_fs2_default"
            android:title="@string/pref_camera2_fs2_title"
            android:entries="@array/pref_camera2_fs2_entries"
            android:entryValues="@array/pref_camera2_fs2_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_burst_limit_key"
            android:defaultValue="@string/pref_camera2_burst_limit_default"
            android:title="@string/pref_camera2_burst_limit_title"
            android:entries="@array/pref_camera2_burst_limit_entries"
            android:entryValues="@array/pref_camera2_burst_limit_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_abort_captures_key"
            android:defaultValue="@string/pref_camera2_abort_captures_default"
            android:title="@string/pref_camera2_abort_captures_title"
            android:entries="@array/pref_camera2_abort_captures_entries"
            android:entryValues="@array/pref_camera2_abort_captures_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_mfhdr_key"
            android:defaultValue="@string/pref_camera2_mfhdr_default"
            android:title="@string/pref_camera2_mfhdr_title"
            android:entries="@array/pref_camera2_mfhdr_entries"
            android:entryValues="@array/pref_camera2_mfhdr_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_gc_shdr_key"
            android:defaultValue="@string/pref_camera2_gc_shdr_default"
            android:title="@string/pref_camera2_gc_shdr_title"
            android:entries="@array/pref_camera2_gc_shdr_entries"
            android:entryValues="@array/pref_camera2_gc_shdr_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_shading_correction_key"
            android:defaultValue="@string/pref_camera2_shading_correction_default"
            android:title="@string/pref_camera2_shading_correction_title"
            android:entries="@array/pref_camera2_shading_correction_entries"
            android:entryValues="@array/pref_camera2_shading_correction_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_extended_max_zoom_key"
            android:defaultValue="@string/pref_camera2_extended_max_zoom_default"
            android:title="@string/pref_camera2_extended_max_zoom_title"
            android:entries="@array/pref_camera2_extended_max_zoom_entries"
            android:entryValues="@array/pref_camera2_extended_max_zoom_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_swpdpc_key"
            android:defaultValue="@string/pref_camera2_swpdpc_default"
            android:title="@string/pref_camera2_swpdpc_title"
            android:entries="@array/pref_camera2_swpdpc_entries"
            android:entryValues="@array/pref_camera2_swpdpc_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_statsnn_control_key"
            android:defaultValue="@string/pref_camera2_statsnn_control_default"
            android:title="@string/pref_camera2_statsnn_control_title"
            android:entries="@array/pref_camera2_statsnn_control_entries"
            android:entryValues="@array/pref_camera2_statsnn_control_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_raw_cb_info_key"
            android:defaultValue="@string/pref_camera2_raw_cb_info_default"
            android:title="@string/pref_camera2_raw_cb_info_title"
            android:entries="@array/pref_camera2_raw_cb_info_entries"
            android:entryValues="@array/pref_camera2_raw_cb_info_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_qll_key"
            android:defaultValue="@string/pref_camera2_qll_default"
            android:title="@string/pref_camera2_qll_title"
            android:entries="@array/pref_camera2_qll_entries"
            android:entryValues="@array/pref_camera2_qll_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_ai_denoiser_key"
            android:defaultValue="@string/pref_camera2_ai_denoiser_default"
            android:title="@string/pref_camera2_ai_denoiser_title"
            android:entries="@array/pref_camera2_ai_denoiser_entries"
            android:entryValues="@array/pref_camera2_ai_denoiser_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_ai_denoiser_format_key"
            android:defaultValue="@string/pref_camera2_ai_denoiser_format_default"
            android:title="@string/pref_camera2_ai_denoiser_format_title"
            android:entries="@array/pref_camera2_ai_denoiser_format_entries"
            android:entryValues="@array/pref_camera2_ai_denoiser_format_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_ai_denoiser_mode_key"
            android:defaultValue="@string/pref_camera2_ai_denoiser_mode_default"
            android:title="@string/pref_camera2_ai_denoiser_mode_title"
            android:entries="@array/pref_camera2_ai_denoiser_mode_entries"
            android:entryValues="@array/pref_camera2_ai_denoiser_mode_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_insensor_zoom_key"
            android:defaultValue="@string/pref_camera2_insensor_zoom_default"
            android:title="@string/pref_camera2_insensor_zoom_title"
            android:entries="@array/pref_camera2_insensor_zoom_entries"
            android:entryValues="@array/pref_camera2_insensor_zoom_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>

        <ListPreference
            android:key="pref_camera2_3A_debug_info_key"
            android:defaultValue="@string/pref_camera2_3A_debug_info_default"
            android:title="@string/pref_camera2_3A_debug_info_title"
            android:entries="@array/pref_camera2_3A_debug_info_entries"
            android:entryValues="@array/pref_camera2_3A_debug_info_entryvalues"
            android:layout="@layout/preference"
            android:summary="%s"/>
    </PreferenceCategory>
</PreferenceScreen>
