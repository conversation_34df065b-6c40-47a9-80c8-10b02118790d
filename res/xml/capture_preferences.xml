<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-2017, The Linux Foundation. All rights reserved.

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are
     met:
      * Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
      * Redistributions in binary form must reproduce the above
        copyright notice, this list of conditions and the following
        disclaimer in the documentation and/or other materials provided
        with the distribution.
      * Neither the name of The Linux Foundation nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

     THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
     WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     MERC<PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
     ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
     BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, <PERSON>XEM<PERSON>AR<PERSON>, OR
     <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
     BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
     OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
     IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

<PreferenceGroup
    xmlns:camera="http://schemas.android.com/apk/res/org.codeaurora.snapcam"
    camera:title="@string/pref_camera_settings_category">
    <IconListPreference
        camera:defaultValue="@string/pref_camera2_flashmode_value_auto"
        camera:entries="@array/pref_camera2_flashmode_entries"
        camera:entryValues="@array/pref_camera2_flashmode_entryvalues"
        camera:key="pref_camera2_flashmode_key"
        camera:singleIcon="@drawable/ic_settings_flash"
        camera:title="@string/pref_camera_flashmode_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_whitebalance_default"
        camera:entries="@array/pref_camera2_whitebalance_entries"
        camera:entryValues="@array/pref_camera2_whitebalance_entryvalues"
        camera:icons="@array/pref_camera2_whitebalance_icons"
        camera:key="pref_camera2_whitebalance_key"
        camera:labelList="@array/pref_camera2_whitebalance_labels"
        camera:largeIcons="@array/pref_camera2_whitebalance_largeicons"
        camera:singleIcon="@drawable/ic_settings_lightsource"
        camera:title="@string/pref_camera_whitebalance_title"/>

    <RecordLocationPreference
        camera:defaultValue="@string/pref_camera_recordlocation_default"
        camera:entries="@array/pref_camera2_recordlocation_entries"
        camera:entryValues="@array/pref_camera2_recordlocation_entryvalues"
        camera:icons="@array/pref_camera2_recordlocation_icons"
        camera:key="pref_camera2_recordlocation_key"
        camera:labelList="@array/pref_camera2_recordlocation_labels"
        camera:largeIcons="@array/pref_camera2_recordlocation_largeicons"
        camera:singleIcon="@drawable/ic_settings_location"
        camera:title="@string/pref_camera_recordlocation_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera_jpegquality_default"
        camera:entries="@array/pref_camera2_jpegquality_entries"
        camera:entryValues="@array/pref_camera2_jpegquality_entryvalues"
        camera:key="pref_camera2_jpegquality_key"
        camera:singleIcon="@drawable/ic_settings_quality"
        camera:title="@string/pref_camera_jpegquality_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera_savepath_default"
        camera:entries="@array/pref_camera2_savepath_entries"
        camera:entryValues="@array/pref_camera2_savepath_entryvalues"
        camera:key="pref_camera2_savepath_key"
        camera:singleIcon="@drawable/ic_settings_storage"
        camera:title="@string/pref_camera_savepath_title"/>

    <ListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_mono_only_entries"
        camera:entryValues="@array/pref_camera2_mono_only_entryvalues"
        camera:key="pref_camera2_mono_only_key"
        camera:title="@string/pref_camera2_mono_only_title"/>

    <ListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_mono_preview_entries"
        camera:entryValues="@array/pref_camera2_mono_preview_entryvalues"
        camera:key="pref_camera2_mono_preview_key"
        camera:title="@string/pref_camera2_mono_preview_title"/>

    <ListPreference
        camera:defaultValue="@string/setting_on_value"
        camera:entries="@array/pref_camera2_clearsight_entries"
        camera:entryValues="@array/pref_camera2_clearsight_entryvalues"
        camera:key="pref_camera2_clearsight_key"
        camera:title="@string/pref_camera2_clearsight_title"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_mpo_default"
        camera:entries="@array/pref_camera2_mpo_entries"
        camera:entryValues="@array/pref_camera2_mpo_entryvalues"
        camera:key="pref_camera2_mpo_key"
        camera:title="@string/pref_camera2_mpo_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_coloreffect_default"
        camera:entries="@array/pref_camera2_coloreffect_entries"
        camera:entryValues="@array/pref_camera2_coloreffect_entryvalues"
        camera:key="pref_camera2_coloreffect_key"
        camera:largeIcons="@array/pref_camera2_coloreffect_icons"
        camera:singleIcon="@drawable/ic_settings_filter"
        camera:thumbnails="@array/pref_camera2_coloreffect_thumbnails"
        camera:title="@string/pref_camera_coloreffect_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_scenemode_default"
        camera:entries="@array/pref_camera2_scenemode_entries"
        camera:entryValues="@array/pref_camera2_scenemode_entryvalues"
        camera:key="pref_camera2_scenemode_key"
        camera:singleIcon="@drawable/ic_settings_scenemode"
        camera:thumbnails="@array/pref_camera2_scenemode_thumbnails"
        camera:title="@string/pref_camera_scenemode_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_scenemode_default"
        camera:entries="@array/pref_camera2_scenemode_instructional_entries"
        camera:entryValues="@array/pref_camera2_scenemode_entryvalues"
        camera:key="pref_camera2_scenemode_instructional"
        camera:thumbnails="@array/pref_camera2_scenemode_black_thumbnails"
        camera:title="@string/pref_camera_scenemode_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_redeyereduction_entries"
        camera:entryValues="@array/pref_camera2_redeyereduction_entryvalues"
        camera:key="pref_camera2_redeyereduction_key"
        camera:singleIcon="@drawable/ic_settings_redeye"
        camera:title="@string/pref_camera_redeyereduction_title"/>

    <IconListPreference
        camera:entries="@array/pref_camera2_picturesize_entries"
        camera:entryValues="@array/pref_camera2_picturesize_entryvalues"
        camera:key="pref_camera2_picturesize_key"
        camera:singleIcon="@drawable/ic_settings_picturesize"
        camera:title="@string/pref_camera_picturesize_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_picture_format_default"
        camera:entries="@array/pref_camera2_picture_format_entries"
        camera:entryValues="@array/pref_camera2_picture_format_entryvalues"
        camera:key="pref_camera2_picture_format_key"
        camera:singleIcon="@drawable/ic_settings_storage"
        camera:title="@string/pref_camera2_picture_format_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_exposure_default"
        camera:key="pref_camera2_exposure_key"
        camera:singleIcon="@drawable/ic_settings_exposure"
        camera:title="@string/pref_exposure_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera_iso_default"
        camera:entries="@array/pref_camera2_iso_entries"
        camera:entryValues="@array/pref_camera2_iso_entryvalues"
        camera:key="pref_camera2_iso_key"
        camera:singleIcon="@drawable/ic_settings_iso"
        camera:title="@string/pref_camera_iso_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_longshot_entries"
        camera:entryValues="@array/pref_camera2_longshot_entryvalues"
        camera:key="pref_camera2_longshot_key"
        camera:singleIcon="@drawable/ic_settings_continuous"
        camera:title="@string/pref_camera_longshot_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_gridline_entries"
        camera:entryValues="@array/pref_camera2_gridline_entryvalues"
        camera:key="pref_camera2_gridline_key"
        camera:singleIcon="@drawable/ic_settings_focus"
        camera:title="@string/pref_camera_gridline_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:key="pref_camera2_selfiemirror_key"
        camera:title="@string/pref_camera_selfiemirror_title"
        camera:entries="@array/pref_camera2_selfiemirror_entries"
        camera:singleIcon="@drawable/ic_settings_selfiemirror"
        camera:entryValues="@array/pref_camera2_selfiemirror_entryvalues" />

    <CountDownTimerPreference
        camera:defaultValue="@string/pref_camera_timer_default"
        camera:key="pref_camera2_timer_key"
        camera:singleIcon="@drawable/ic_settings_countdowntimer"
        camera:title="@string/pref_camera_timer_title"/>

    <ListPreference
        camera:defaultValue="0"
        camera:key="pref_camera2_initial_camera_key"
        camera:entries="@array/pref_camera2_initial_camera_entries"
        camera:entryValues="@array/pref_camera2_initial_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="0"
        camera:key="pref_camera2_makeup_key"
        camera:entries="@array/pref_camera2_makeup_entries"
        camera:entryValues="@array/pref_camera2_makeup_entryvalues"
        camera:title="@string/pref_camera2_makeup_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera2_video_quality_default"
        camera:entries="@array/pref_camera2_video_quality_entries"
        camera:entryValues="@array/pref_camera2_video_quality_entryvalues"
        camera:key="pref_camera2_video_quality_key"
        camera:singleIcon="@drawable/ic_settings_quality"
        camera:title="@string/pref_video_quality_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera_video_duration_default"
        camera:entries="@array/pref_camera2_video_duration_entries"
        camera:entryValues="@array/pref_camera2_video_duration_entryvalues"
        camera:key="pref_camera2_video_duration_key"
        camera:singleIcon="@drawable/ic_settings_duration"
        camera:title="@string/pref_camera_video_duration_title"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera_videoencoder_default"
        camera:entries="@array/pref_camera2_videoencoder_entries"
        camera:entryValues="@array/pref_camera2_videoencoder_entryvalues"
        camera:key="pref_camera2_videoencoder_key"
        camera:title="@string/pref_camera_videoencoder_title"/>
    <ListPreference
        camera:defaultValue="@string/pref_camera_audioencoder_default"
        camera:entries="@array/pref_camera2_audioencoder_entries"
        camera:entryValues="@array/pref_camera2_audioencoder_entryvalues"
        camera:key="pref_camera2_audioencoder_key"
        camera:title="@string/pref_camera_audioencoder_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_dis_entries"
        camera:entryValues="@array/pref_camera2_dis_entryvalues"
        camera:key="pref_camera2_dis_key"
        camera:singleIcon="@drawable/ic_eis_menu"
        camera:title="@string/pref_camera_dis_title"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_noise_reduction_default"
        camera:entries="@array/pref_camera2_noise_reduction_entries"
        camera:entryValues="@array/pref_camera2_noise_reduction_entryvalues"
        camera:key="pref_camera2_noise_reduction_key"
        camera:title="@string/pref_camera2_noise_reduction_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_camera_video_flashmode_default"
        camera:entries="@array/pref_camera2_video_flashmode_entries"
        camera:entryValues="@array/pref_camera2_video_flashmode_entryvalues"
        camera:icons="@array/video_flashmode_icons"
        camera:key="pref_camera2_video_flashmode_key"
        camera:singleIcon="@drawable/ic_settings_flash"
        camera:title="@string/pref_camera_flashmode_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera_video_rotation_default"
        camera:entries="@array/pref_camera2_video_rotation_entries"
        camera:entryValues="@array/pref_camera2_video_rotation_entryvalues"
        camera:key="pref_camera2_video_rotation_key"
        camera:title="@string/pref_camera_video_rotation_title"/>

    <IconListPreference
        camera:defaultValue="@string/pref_video_time_lapse_frame_interval_default"
        camera:entries="@array/pref_camera2_video_time_lapse_frame_interval_entries"
        camera:entryValues="@array/pref_camera2_video_time_lapse_frame_interval_entryvalues"
        camera:key="pref_camera2_video_time_lapse_frame_interval_key"
        camera:title="@string/pref_video_time_lapse_frame_interval_title"/>

    <IconListPreference
        camera:defaultValue="@string/setting_off_value"
        camera:entries="@array/pref_camera2_facedetection_entries"
        camera:entryValues="@array/pref_camera2_facedetection_entryvalues"
        camera:key="pref_camera2_facedetection_key"
        camera:singleIcon="@drawable/ic_settings_facerec"
        camera:title="@string/pref_camera_facedetection_title"/>

    <IconListPreference
        camera:entries="@array/pref_camera_hfr_entries"
        camera:entryValues="@array/pref_camera_hfr_entryvalues"
        camera:key="pref_camera2_hfr_key"
        camera:singleIcon="@drawable/ic_settings_fps"
        camera:title="@string/pref_camera_hfr_title" />

    <IconListPreference
        camera:defaultValue="@string/pref_selfie_flash_default"
        camera:entries="@array/pref_selfie_flash_entries"
        camera:entryValues="@array/pref_selfie_flash_entryvalues"
        camera:key="pref_selfie_flash_key"
        camera:singleIcon="@drawable/ic_settings_flash"
        camera:title="@string/pref_selfie_flash_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_shutter_sound_default"
        camera:entries="@array/pref_camera2_shutter_sound_entries"
        camera:entryValues="@array/pref_camera2_shutter_sound_entryvalues"
        camera:key="pref_camera2_shutter_sound_key"
        camera:title="@string/pref_camera2_shutter_sound_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_touch_track_focus_default"
        camera:entries="@array/pref_camera2_touch_track_focus_entries"
        camera:entryValues="@array/pref_camera2_touch_track_focus_entryvalues"
        camera:key="pref_camera2_touch_track_focus_key"
        camera:title="@string/pref_camera2_touch_track_focus_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_instant_aec_default"
        camera:entries="@array/pref_camera2_instant_aec_entries"
        camera:entryValues="@array/pref_camera2_instant_aec_entryvalues"
        camera:key="pref_camera2_instant_aec_key"
        camera:title="@string/pref_camera2_instant_aec_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_saturation_level_default"
        camera:entries="@array/pref_camera2_saturation_level_entries"
        camera:entryValues="@array/pref_camera2_saturation_level_entryvalues"
        camera:key="pref_camera2_saturation_level_key"
        camera:title="@string/pref_camera2_saturation_level_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_anti_banding_level_default"
        camera:entries="@array/pref_camera2_anti_banding_level_entries"
        camera:entryValues="@array/pref_camera2_anti_banding_level_entryvalues"
        camera:key="pref_camera2_anti_banding_level_key"
        camera:title="@string/pref_camera2_anti_banding_level_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_auto_hdr_default"
        camera:entries="@array/pref_camera2_auto_hdr_entries"
        camera:entryValues="@array/pref_camera2_auto_hdr_entryvalues"
        camera:key="pref_camera2_auto_hdr_key"
        camera:title="@string/pref_camera2_auto_hdr_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_hdr_default"
        camera:entries="@array/pref_camera2_hdr_entries"
        camera:entryValues="@array/pref_camera2_hdr_entryvalues"
        camera:key="pref_camera2_hdr_key"
        camera:title="@string/pref_camera2_hdr_title" />

    <ListPreference
        camera:key="pref_camera2_saveraw_key"
        camera:defaultValue="@string/pref_camera2_saveraw_default"
        camera:title="@string/pref_camera2_saveraw_title"
        camera:entries="@array/pref_camera2_saveraw_entries"
        camera:entryValues="@array/pref_camera2_saveraw_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_zsl_key"
        camera:defaultValue="@string/pref_camera2_zsl_default"
        camera:title="@string/pref_camera2_zsl_title"
        camera:entries="@array/pref_camera2_zsl_entries"
        camera:entryValues="@array/pref_camera2_zsl_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_zoom_key"
        camera:defaultValue="@string/pref_camera_zoom_default"
        camera:title="@string/pref_camera_zoom_title"
        camera:entries="@array/pref_camera_zoom_entries"
        camera:entryValues="@array/pref_camera_zoom_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_manual_exp_key"
        camera:defaultValue="@string/pref_camera_manual_exp_default"
        camera:title="@string/pref_camera_manual_exp_title"
        camera:entries="@array/pref_camera_manual_exp_entries"
        camera:entryValues="@array/pref_camera_manual_exp_entry_values" />

    <ListPreference
        camera:key="pref_camera2_tone_mapping_key"
        camera:defaultValue="@string/pref_camera_tone_mapping_default"
        camera:title="@string/pref_camera_tone_mapping_title"
        camera:entries="@array/pref_camera_tone_mapping_entries"
        camera:entryValues="@array/pref_camera_tone_mapping_entry_values" />

    <ListPreference
        camera:defaultValue="@string/pref_camera_manual_wb_default"
        camera:entries="@array/pref_camera_manual_wb_entries"
        camera:entryValues="@array/pref_camera_manual_wb_entry_values"
        camera:key="pref_camera2_manual_wb_key"
        camera:title="@string/pref_camera_manual_wb_title" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_qcfa_default"
        camera:entries="@array/pref_camera2_qcfa_entries"
        camera:entryValues="@array/pref_camera2_qcfa_entryvalues"
        camera:key="pref_camera2_qcfa_key"
        camera:title="@string/pref_camera2_qcfa_title" />

    <ListPreference
        camera:key="pref_camera2_fd_smile_key"
        camera:defaultValue="@string/pref_camera2_bsgc_default"
        camera:title="@string/pref_camera2_fd_smile_title"
        camera:entries="@array/pref_camera2_bsgc_entries"
        camera:entryValues="@array/pref_camera2_bsgc_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_fd_gaze_key"
        camera:defaultValue="@string/pref_camera2_bsgc_default"
        camera:title="@string/pref_camera2_fd_gaze_title"
        camera:entries="@array/pref_camera2_bsgc_entries"
        camera:entryValues="@array/pref_camera2_bsgc_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_fd_blink_key"
        camera:defaultValue="@string/pref_camera2_bsgc_default"
        camera:title="@string/pref_camera2_fd_blink_title"
        camera:entries="@array/pref_camera2_bsgc_entries"
        camera:entryValues="@array/pref_camera2_bsgc_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_facial_contour_key"
        camera:defaultValue="@string/pref_camera2_bsgc_default"
        camera:title="@string/pref_camera2_facial_contour_title"
        camera:entries="@array/pref_camera2_facial_contour_entries"
        camera:entryValues="@array/pref_camera2_facial_contour_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_face_detection_mode"
        camera:defaultValue="@string/pref_camera2_face_detection_mode_default"
        camera:title="@string/pref_camera2_face_detection_mode_title"
        camera:entries="@array/pref_camera2_face_detection_entries"
        camera:entryValues="@array/pref_camera2_face_detection_entryvalues" />


    <ListPreference
        camera:key="pref_camera2_videoencoderprofile_key"
        camera:defaultValue="@string/pref_camera2_videoencoderprofile_default"
        camera:title="@string/pref_camera2_videoencoderprofile_title"
        camera:entries="@array/pref_camera2_videoencoderprofile_entry"
        camera:entryValues="@array/pref_camera2_videoencoderprofile_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_sharpness_control_key"
        camera:defaultValue="@string/pref_camera2_sharpness_control_default"
        camera:title="@string/pref_camera2_sharpness_control_title"
        camera:entries="@array/pref_camera2_sharpness_control_entries"
        camera:entryValues="@array/pref_camera2_multilevel_sharpness_entryvalues"/>

    <ListPreference
        camera:key="pref_camera2_afmode_key"
        camera:defaultValue="@string/pref_camera2_afmode_default"
        camera:title="@string/pref_camera2_afmode_title"
        camera:entries="@array/pref_camera2_afmode_entries"
        camera:entryValues="@array/pref_camera2_afmode_entryvalues"/>

    <ListPreference
        camera:key="pref_camera2_exposure_metering_key"
        camera:defaultValue="@string/pref_camera2_exposure_metering_default"
        camera:title="@string/pref_camera2_exposure_metering_title"
        camera:entries="@array/pref_camera2_exposure_metering_entries"
        camera:entryValues="@array/pref_camera2_exposure_metering_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_eis_key"
        camera:defaultValue="@string/pref_camera2_eis_default"
        camera:title="@string/pref_camera2_eis_title"
        camera:entries="@array/pref_camera2_eis_entries"
        camera:entryValues="@array/pref_camera2_eis_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_fovc_key"
        camera:defaultValue="@string/pref_camera2_fovc_default"
        camera:title="@string/pref_camera2_fovc_title"
        camera:entries="@array/pref_camera2_fovc_entries"
        camera:entryValues="@array/pref_camera2_fovc_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_video_hdr_key"
        camera:defaultValue="@string/pref_camera2_video_hdr_default"
        camera:title="@string/pref_camera2_video_hdr_title"
        camera:entries="@array/pref_camera2_video_hdr_entries"
        camera:entryValues="@array/pref_camera2_video_hdr_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_force_aux_key"
        camera:defaultValue="@string/pref_camera2_force_aux_default"
        camera:title="@string/pref_camera2_force_aux_title"
        camera:entries="@array/pref_camera2_force_aux_entries"
        camera:entryValues="@array/pref_camera2_force_aux_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_oncapturebufferlost_key"
        camera:defaultValue="@string/pref_camera2_oncapturebufferlost_default"
        camera:title="@string/pref_camera2_oncapturebufferlost_title"
        camera:entries="@array/pref_camera2_oncapturebufferlost_entries"
        camera:entryValues="@array/pref_camera2_oncapturebufferlost_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_switch_camera_default"
        camera:key="pref_camera2_switch_camera_key"
        camera:title="@string/pref_camera2_switch_camera_title"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_select_mode_default"
        camera:key="pref_camera2_select_mode_key"
        camera:title="@string/pref_camera2_select_mode_title"
        camera:entries="@array/pref_camera2_select_mode_entries"
        camera:entryValues="@array/pref_camera2_select_mode_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_multi_camera_mode_default"
        camera:key="pref_camera2_multi_camera_mode_key"
        camera:title="@string/pref_camera2_multi_camera_mode_title"
        camera:entries="@array/pref_camera2_multi_camera_mode_entries"
        camera:entryValues="@array/pref_camera2_multi_camera_mode_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_raw_reprocess_default"
        camera:key="pref_camera2_raw_reprocess_key"
        camera:title="@string/pref_camera2_raw_reprocess_title"
        camera:entries="@array/pref_camera2_raw_reprocess_entries"
        camera:entryValues="@array/pref_camera2_raw_reprocess_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_raw_format_default"
        camera:key="pref_camera2_raw_format_key"
        camera:title="@string/pref_camera2_raw_format_title"
        camera:entries="@array/pref_camera2_raw_format_entries"
        camera:entryValues="@array/pref_camera2_raw_format_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_rawinfo_type_default"
        camera:key="pref_camera2_rawinfo_type_key"
        camera:title="@string/pref_camera2_rawinfo_type_title"
        camera:entries="@array/pref_camera2_rawinfo_type_entries"
        camera:entryValues="@array/pref_camera2_rawinfo_type_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_camera_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>


    <ListPreference
        camera:defaultValue="@string/pref_camera2_single_physical_camera_default"
        camera:key="pref_camera2_single_physical_camera_key"
        camera:title="@string/pref_camera2_single_physical_camera_title"
        camera:entries="@array/pref_camera2_single_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_single_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_jpeg_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_camcorder_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_yuv_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_raw_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_hdr_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:defaultValue="@string/pref_camera2_physical_camera_default"
        camera:key="pref_camera2_physical_mfnr_key"
        camera:title="@string/pref_camera2_physical_camera_title"
        camera:entries="@array/pref_camera2_physical_camera_entries"
        camera:entryValues="@array/pref_camera2_physical_camera_entryvalues"/>

    <ListPreference
        camera:entries="@array/pref_camera2_picturesize_entries"
        camera:entryValues="@array/pref_camera2_picturesize_entryvalues"
        camera:key="pref_camera2_physical_size_0_key"
        camera:title="@string/pref_camera2_physical_size_title"/>
    <ListPreference
        camera:entries="@array/pref_camera2_picturesize_entries"
        camera:entryValues="@array/pref_camera2_picturesize_entryvalues"
        camera:key="pref_camera2_physical_size_1_key"
        camera:title="@string/pref_camera2_physical_size_title"/>
    <ListPreference
        camera:entries="@array/pref_camera2_picturesize_entries"
        camera:entryValues="@array/pref_camera2_picturesize_entryvalues"
        camera:key="pref_camera2_physical_size_2_key"
        camera:title="@string/pref_camera2_physical_size_title"/>
    <ListPreference
        camera:defaultValue="@string/pref_camera2_video_quality_default"
        camera:entries="@array/pref_camera2_video_quality_entries"
        camera:entryValues="@array/pref_camera2_video_quality_entryvalues"
        camera:key="pref_camera2_physical_quality_0_key"
        camera:title="@string/pref_camera2_physical_quality_title"/>
    <ListPreference
        camera:defaultValue="@string/pref_camera2_video_quality_default"
        camera:entries="@array/pref_camera2_video_quality_entries"
        camera:entryValues="@array/pref_camera2_video_quality_entryvalues"
        camera:key="pref_camera2_physical_quality_1_key"
        camera:title="@string/pref_camera2_physical_quality_title"/>
    <ListPreference
        camera:defaultValue="@string/pref_camera2_video_quality_default"
        camera:entries="@array/pref_camera2_video_quality_entries"
        camera:entryValues="@array/pref_camera2_video_quality_entryvalues"
        camera:key="pref_camera2_physical_quality_2_key"
        camera:title="@string/pref_camera2_physical_quality_title"/>
    <ListPreference
        camera:key="pref_camera2_switcher_key"
        camera:defaultValue="@string/pref_camera2_switcher_default"
        camera:title="@string/pref_camera2_switcher_title"
        camera:entries="@array/pref_camera2_switcher_entries"
        camera:entryValues="@array/pref_camera2_switcher_entryvalues" />
    <ListPreference
        camera:key="pref_camera2_stats_visualizer_key"
        camera:defaultValue="@string/pref_camera2_stats_visualizer_default"
        camera:title="@string/pref_camera2_stats_visualizer_title"
        camera:entries="@array/pref_camera2_stats_visualizer_entries"
        camera:entryValues="@array/pref_camera2_stats_visualizer_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_variable_fps_key"
        camera:defaultValue="@string/pref_camera2_variable_fps_default"
        camera:title="@string/pref_camera2_variable_fps_title"
        camera:entries="@array/pref_camera2_variable_fps_entries"
        camera:entryValues="@array/pref_camera2_variable_fps_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_video_flip_key"
        camera:defaultValue="@string/pref_camera2_video_flip_default"
        camera:title="@string/pref_camera2_video_flip_title"
        camera:entries="@array/pref_camera2_video_flip_entries"
        camera:entryValues="@array/pref_camera2_video_flip_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_capture_mfnr_key"
        camera:defaultValue="@string/pref_camera2_capture_mfnr_default"
        camera:title="@string/pref_camera2_capture_mfnr_title"
        camera:entries="@array/pref_camera2_capture_mfnr_entries"
        camera:entryValues="@array/pref_camera2_capture_mfnr_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_live_preview_key"
        camera:defaultValue="@string/pref_camera2_live_preview_default"
        camera:title="@string/pref_camera2_live_preview_title"
        camera:entries="@array/pref_camera2_live_preview_entries"
        camera:entryValues="@array/pref_camera2_live_preview_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_fs2_key"
        camera:defaultValue="@string/pref_camera2_fs2_default"
        camera:title="@string/pref_camera2_fs2_title"
        camera:entries="@array/pref_camera2_fs2_entries"
        camera:entryValues="@array/pref_camera2_fs2_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_burst_limit_key"
        camera:defaultValue="@string/pref_camera2_burst_limit_default"
        camera:title="@string/pref_camera2_burst_limit_title"
        camera:entries="@array/pref_camera2_burst_limit_entries"
        camera:entryValues="@array/pref_camera2_burst_limit_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_abort_captures_key"
        camera:defaultValue="@string/pref_camera2_abort_captures_default"
        camera:title="@string/pref_camera2_abort_captures_title"
        camera:entries="@array/pref_camera2_abort_captures_entries"
        camera:entryValues="@array/pref_camera2_abort_captures_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_mfhdr_key"
        camera:defaultValue="@string/pref_camera2_mfhdr_default"
        camera:title="@string/pref_camera2_mfhdr_title"
        camera:entries="@array/pref_camera2_mfhdr_entries"
        camera:entryValues="@array/pref_camera2_mfhdr_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_gc_shdr_key"
        camera:defaultValue="@string/pref_camera2_gc_shdr_default"
        camera:title="@string/pref_camera2_gc_shdr_title"
        camera:entries="@array/pref_camera2_gc_shdr_entries"
        camera:entryValues="@array/pref_camera2_gc_shdr_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_shading_correction_key"
        camera:defaultValue="@string/pref_camera2_shading_correction_default"
        camera:title="@string/pref_camera2_shading_correction_title"
        camera:entries="@array/pref_camera2_shading_correction_entries"
        camera:entryValues="@array/pref_camera2_shading_correction_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_extended_max_zoom_key"
        camera:defaultValue="@string/pref_camera2_extended_max_zoom_default"
        camera:title="@string/pref_camera2_extended_max_zoom_title"
        camera:entries="@array/pref_camera2_extended_max_zoom_entries"
        camera:entryValues="@array/pref_camera2_extended_max_zoom_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_hvx_shdr_key"
        camera:defaultValue="@string/pref_camera2_hvx_shdr_default"
        camera:title="@string/pref_camera2_hvx_shdr_title"
        camera:entries="@array/pref_camera2_hvx_shdr_entries"
        camera:entryValues="@array/pref_camera2_hvx_shdr_entry_values" />

    <ListPreference
        camera:key="pref_camera2_hvx_mfhdr_key"
        camera:defaultValue="@string/pref_camera2_hvx_mfhdr_default"
        camera:title="@string/pref_camera2_hvx_mfhdr_title"
        camera:entries="@array/pref_camera2_hvx_mfhdr_entries"
        camera:entryValues="@array/pref_camera2_hvx_mfhdr_entry_values" />

    <ListPreference
        camera:key="pref_camera2_swpdpc_key"
        camera:defaultValue="@string/pref_camera2_swpdpc_default"
        camera:title="@string/pref_camera2_swpdpc_title"
        camera:entries="@array/pref_camera2_swpdpc_entries"
        camera:entryValues="@array/pref_camera2_swpdpc_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_statsnn_control_key"
        camera:defaultValue="@string/pref_camera2_statsnn_control_default"
        camera:title="@string/pref_camera2_statsnn_control_title"
        camera:entries="@array/pref_camera2_statsnn_control_entries"
        camera:entryValues="@array/pref_camera2_statsnn_control_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_raw_cb_info_key"
        camera:defaultValue="@string/pref_camera2_raw_cb_info_default"
        camera:title="@string/pref_camera2_raw_cb_info_title"
        camera:entries="@array/pref_camera2_raw_cb_info_entries"
        camera:entryValues="@array/pref_camera2_raw_cb_info_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_qll_key"
        camera:defaultValue="@string/pref_camera2_qll_default"
        camera:title="@string/pref_camera2_qll_title"
        camera:entries="@array/pref_camera2_qll_entries"
        camera:entryValues="@array/pref_camera2_qll_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_ai_denoiser_key"
        camera:defaultValue="@string/pref_camera2_ai_denoiser_default"
        camera:title="@string/pref_camera2_ai_denoiser_title"
        camera:entries="@array/pref_camera2_ai_denoiser_entries"
        camera:entryValues="@array/pref_camera2_ai_denoiser_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_ai_denoiser_format_key"
        camera:defaultValue="@string/pref_camera2_ai_denoiser_format_default"
        camera:title="@string/pref_camera2_ai_denoiser_format_title"
        camera:entries="@array/pref_camera2_ai_denoiser_format_entries"
        camera:entryValues="@array/pref_camera2_ai_denoiser_format_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_ai_denoiser_mode_key"
        camera:defaultValue="@string/pref_camera2_ai_denoiser_mode_default"
        camera:title="@string/pref_camera2_ai_denoiser_mode_title"
        camera:entries="@array/pref_camera2_ai_denoiser_mode_entries"
        camera:entryValues="@array/pref_camera2_ai_denoiser_mode_entryvalues" />

    <ListPreference
        camera:key="pref_camera2_insensor_zoom_key"
        camera:defaultValue="@string/pref_camera2_insensor_zoom_default"
        camera:title="@string/pref_camera2_insensor_zoom_title"
        camera:entries="@array/pref_camera2_insensor_zoom_entries"
        camera:entryValues="@array/pref_camera2_insensor_zoom_entryvalues" />

    <ListPreference
        camera:defaultValue="@string/pref_camera2_3A_debug_info_default"
        camera:key="pref_camera2_3A_debug_info_key"
        camera:title="@string/pref_camera2_3A_debug_info_title"
        camera:entries="@array/pref_camera2_3A_debug_info_entries"
        camera:entryValues="@array/pref_camera2_3A_debug_info_entryvalues"/>
</PreferenceGroup>
