<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2014, The Linux Foundation. All rights reserved.

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are
     met:
         * Redistributions of source code must retain the above copyright
           notice, this list of conditions and the following disclaimer.
         * Redistributions in binary form must reproduce the above
           copyright notice, this list of conditions and the following
           disclaimer in the documentation and/or other materials provided
           with the distribution.
         * Neither the name of The Linux Foundation nor the names of its
           contributors may be used to endorse or promote products derived
           from this software without specific prior written permission.

     THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
     WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     ME<PERSON><PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
     ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
     BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
     CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
     BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
     OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
     IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/shutter_button_pressed"
    android:oneshot="true" >

    <item
        android:drawable="@drawable/shutter_button_0"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_1"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_2"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_3"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_4"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_5"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_6"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_5"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_4"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_3"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_2"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_1"
        android:duration="33"/>
    <item
        android:drawable="@drawable/shutter_button_0"
        android:duration="33"/>

</animation-list>
