<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2012 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/camera_controls"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/default_background" >

    <ImageView
        android:id="@+id/shutter_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/shutter_offset"
        android:contentDescription="@string/accessibility_shutter_button"
        android:scaleType="center"
        android:src="@drawable/btn_new_shutter" />

    <include
        android:layout_width="64dip"
        android:layout_height="64dip"
        android:layout_above="@id/shutter_button"
        android:layout_alignParentRight="true"
        android:layout_marginRight="6dip"
        android:layout_marginTop="-5dip"
        layout="@layout/menu_indicators_keyguard" />

    <ImageView
        android:id="@+id/camera_switcher"
        style="@style/SwitcherButton"
        android:layout_below="@id/shutter_button"
        android:layout_alignParentRight="true"
        android:layout_marginRight="2dip"
        android:contentDescription="@string/accessibility_mode_picker"
        android:scaleType="center"
        android:src="@drawable/ic_switch_camera" />

    <ImageView
        android:id="@+id/camera_switcher_ind"
        style="@style/SwitcherButton"
        android:layout_below="@id/shutter_button"
        android:layout_alignParentRight="true"
        android:layout_marginRight="2dip"
        android:contentDescription="@string/accessibility_mode_picker"
        android:scaleType="center"
        android:src="@drawable/ic_switcher_menu_indicator" />

</RelativeLayout>
