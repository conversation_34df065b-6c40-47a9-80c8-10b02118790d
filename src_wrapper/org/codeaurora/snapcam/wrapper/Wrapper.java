/*
 * Copyright (c) 2017, The Linux Foundation. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above
 *    copyright notice, this list of conditions and the following
 *    disclaimer in the documentation and/or other materials provided
 *    with the distribution.
 *  * Neither the name of The Linux Foundation nor the names of its
 *    contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
 * BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * <PERSON><PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
 * BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN
 * IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

package org.codeaurora.snapcam.wrapper;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

import android.util.Log;

public class Wrapper{
    protected final static boolean DEBUG = false;
    protected final static String TAG = "Wrapper";

    protected static int getFieldValue(Field field, int def){
        int value = def;
        if ( field != null ) {
            try {
                value = (int) field.get(null);
            }catch (Exception exception){
                exception.printStackTrace();
            }
        }
        return value;
    }

    protected static String getFieldValue(Field field, String def){
        String value = def;
        if ( field != null ) {
            try {
                value = (String) field.get(null);
            }catch (Exception exception){
                exception.printStackTrace();
            }
        }
        return value;
    }
    protected static Field getField(Class<?> classInstance, String name) {
        Log.d(TAG, "getField:" + classInstance + " field:"+ name);
        if ( DEBUG ){
            Log.e(TAG, "" + classInstance + " no " + name);
            return null;
        }

        Field field = null;
        try{
            field = classInstance.getField(name);
            Log.d(TAG, "getField:" + classInstance + " " + name);
        }catch (Exception exception){
            exception.printStackTrace();
        }
        return field;
    }
}
