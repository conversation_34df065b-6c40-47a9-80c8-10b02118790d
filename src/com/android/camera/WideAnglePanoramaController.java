/*
 * Copyright (C) 2013 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.camera;

/**
 * The interface that controls the wide angle panorama module.
 */
public interface WideAnglePanoramaController {

    public void onPreviewUIReady();

    public void onPreviewUIDestroyed();

    public void cancelHighResStitching();

    public void onShutterButtonClick();

    public void onPreviewUILayoutChange(int l, int t, int r, int b);

    public int getCameraOrientation();
}
