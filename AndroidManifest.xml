<?xml version="1.0" encoding="utf-8"?>

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.codeaurora.snapcam">

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="33" />

    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.SET_ORIENTATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="false" />

    <application
        android:name="com.android.camera.app.CameraApp"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher_camera"
        android:label="@string/snapcam_app_name"
        android:largeHeap="true"
        android:logo="@mipmap/ic_launcher_camera"
        android:restoreAnyVersion="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.Camera" >
        <activity
            android:name="com.android.camera.CameraActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/snapcam_app_name"
            android:launchMode="singleTask"
            android:logo="@mipmap/ic_launcher_gallery"
            android:screenOrientation="landscape"
            android:taskAffinity="com.android.camera.CameraActivity"
            android:theme="@style/Theme.Camera"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"
            android:visibleToInstantApps="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="com.android.keyguard.layout"
                android:resource="@layout/keyguard_widget" />
        </activity>

        <activity
                android:name="com.android.camera.PermissionsActivity"
                android:label="@string/app_name"
                android:launchMode="singleTop"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:parentActivityName="com.android.camera.CameraActivity"
                android:visibleToInstantApps="true"
            android:exported="true">

                <intent-filter>
                    <action android:name="android.media.action.VIDEO_CAMERA" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.VOICE"/>
                </intent-filter>

                <intent-filter>
                    <action android:name="android.media.action.VIDEO_CAPTURE" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.VOICE"/>
                </intent-filter>

                <intent-filter>
                    <action android:name="android.media.action.IMAGE_CAPTURE" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.VOICE"/>
                </intent-filter>

                <intent-filter>
                    <action android:name="android.media.action.STILL_IMAGE_CAMERA" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.VOICE"/>
                </intent-filter>

            <intent-filter>
                <action android:name="android.media.action.TOF_CAMERA" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

                <meta-data
                        android:name="android.support.PARENT_ACTIVITY"
                        android:value="com.android.camera.CameraActivity" />
        </activity>

        <activity
            android:theme="@style/Theme.Settings"
            android:name="com.android.camera.SettingsActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/snapcam_app_name"
            android:launchMode="singleTop"
            android:parentActivityName="com.android.camera.CameraActivity">
        </activity>

        <activity
            android:theme="@android:style/Theme.Material.Light.NoActionBar.Fullscreen"
            android:name="com.android.camera.SceneModeActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/snapcam_app_name"
            android:launchMode="singleTop" >
        </activity>

        <activity-alias
            android:name="com.android.camera.CameraLauncher"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/snapcam_app_name"
            android:launchMode="singleTop"
            android:targetActivity="com.android.camera.CameraActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.android.camera.SecureCameraActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/snapcam_app_name"
            android:launchMode="singleInstance"
            android:logo="@mipmap/ic_launcher_gallery"
            android:screenOrientation="portrait"
            android:taskAffinity="com.android.camera.SecureCameraActivity"
            android:theme="@style/Theme.Camera"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan"
            android:exported="true">
            <intent-filter>
                <action android:name="android.media.action.STILL_IMAGE_CAMERA_SECURE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.media.action.IMAGE_CAPTURE_SECURE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="com.android.keyguard.layout"
                android:resource="@layout/keyguard_widget" />
        </activity>

        <activity-alias
            android:name="com.android.camera.CameraGestureActivity"
            android:icon="@mipmap/ic_launcher_camera"
            android:label="@string/camera_gesture_title"
            android:targetActivity="com.android.camera.SecureCameraActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.android.camera.crop.CropActivity"
            android:label="@string/crop_action"
            android:theme="@style/Theme.Crop"
            android:configChanges="keyboardHidden|orientation|screenSize">
        </activity>

        <activity
            android:name="com.android.camera.RefocusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize">
        </activity>

        <activity
            android:name="com.android.camera.TofCameraActivity"
            android:configChanges="keyboardHidden|orientation|screenSize">
        </activity>

        <activity
            android:name="com.android.camera.BestpictureActivity"
            android:configChanges="keyboardHidden|orientation|screenSize">
        </activity>

        <receiver android:name="com.android.camera.DisableCameraReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.android.camera.CameraButtonIntentReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.CAMERA_BUTTON"/>
            </intent-filter>
        </receiver>

        <service android:name="com.android.camera.MediaSaveService" />
        <service android:name="com.android.camera.AIDenoiserService" />
    </application>

</manifest>
